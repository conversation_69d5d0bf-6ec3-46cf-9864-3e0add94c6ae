# Chrome扩展测试指南

## 安装测试步骤

### 1. 准备测试环境
1. 打开Chrome浏览器
2. 确保有多个标签页和窗口用于测试
3. 进入扩展管理页面：`chrome://extensions/`
4. 开启"开发者模式"

### 2. 加载扩展
1. 点击"加载已解压的扩展程序"
2. 选择项目根目录（包含manifest.json的文件夹）
3. 确认扩展出现在列表中，没有错误提示

### 3. 基础功能测试

#### 3.1 扩展加载测试
- [ ] 扩展成功加载，没有错误
- [ ] 工具栏显示扩展图标（可能是默认图标）
- [ ] 扩展状态为"已启用"

#### 3.2 Service Worker测试
1. 在扩展管理页面点击"Service Worker"链接
2. 查看控制台输出：
   - [ ] 看到"Service Worker initializing..."
   - [ ] 看到"Service Worker initialized successfully"
   - [ ] 看到"Browser data sync completed"
   - [ ] 没有错误信息

#### 3.3 Popup界面测试
1. 点击扩展图标
2. 验证popup界面：
   - [ ] Popup窗口正常打开
   - [ ] 界面元素正确显示（标题、搜索框、按钮等）
   - [ ] 没有JavaScript错误
   - [ ] 显示当前浏览器的标签页和窗口信息

#### 3.4 数据显示测试
1. 在popup中查看数据：
   - [ ] 统计信息正确显示（标签页数、窗口数）
   - [ ] 窗口列表正确显示
   - [ ] 标签页信息正确显示（标题、URL、图标）
   - [ ] 数据与实际浏览器状态一致

#### 3.5 搜索功能测试
1. 在搜索框中输入关键词：
   - [ ] 搜索结果实时更新
   - [ ] 清除按钮正常工作
   - [ ] 筛选按钮正常工作
   - [ ] 搜索结果准确

#### 3.6 交互功能测试
1. 测试标签页操作：
   - [ ] 点击标签页可以激活
   - [ ] 关闭按钮可以关闭标签页
   - [ ] 操作后popup自动关闭或更新

2. 测试窗口操作：
   - [ ] 点击窗口标题可以展开/折叠
   - [ ] 聚焦按钮可以切换窗口
   - [ ] 窗口菜单正常显示

3. 测试底部按钮：
   - [ ] 新建窗口按钮工作正常
   - [ ] 同步按钮可以刷新数据
   - [ ] 导入/导出按钮显示提示信息

### 4. 数据存储测试

#### 4.1 Chrome Storage检查
1. 在Service Worker控制台中运行：
```javascript
chrome.storage.local.get(null, (data) => console.log(data));
```
2. 验证存储的数据：
   - [ ] 存在tabs数据
   - [ ] 存在windows数据
   - [ ] 存在settings数据
   - [ ] 数据结构正确

#### 4.2 数据同步测试
1. 打开新标签页或窗口
2. 点击同步按钮
3. 验证：
   - [ ] 新数据被正确获取
   - [ ] 界面正确更新
   - [ ] 存储数据被更新

### 5. 错误处理测试

#### 5.1 权限测试
1. 检查扩展权限：
   - [ ] tabs权限正常工作
   - [ ] storage权限正常工作
   - [ ] windows权限正常工作

#### 5.2 异常情况测试
1. 测试异常情况：
   - [ ] 关闭所有标签页时的处理
   - [ ] 网络错误时的处理
   - [ ] 权限不足时的处理

### 6. 性能测试

#### 6.1 响应速度测试
1. 测试大量标签页情况：
   - [ ] 打开50+标签页时的响应速度
   - [ ] 搜索功能的响应速度
   - [ ] 数据同步的速度

#### 6.2 内存使用测试
1. 在Chrome任务管理器中查看：
   - [ ] 扩展内存使用合理
   - [ ] 没有内存泄漏
   - [ ] Service Worker正常运行

### 7. 兼容性测试

#### 7.1 Chrome版本测试
- [ ] 在最新版Chrome中正常工作
- [ ] 在较旧版Chrome中的兼容性

#### 7.2 操作系统测试
- [ ] Windows系统正常工作
- [ ] macOS系统正常工作
- [ ] Linux系统正常工作

## 常见问题排查

### 问题1：扩展无法加载
**可能原因：**
- manifest.json语法错误
- 文件路径错误
- 权限配置问题

**排查方法：**
1. 检查扩展管理页面的错误信息
2. 验证manifest.json语法
3. 确认所有文件路径正确

### 问题2：Service Worker无法启动
**可能原因：**
- JavaScript语法错误
- 模块导入问题
- API调用错误

**排查方法：**
1. 查看Service Worker控制台
2. 检查JavaScript语法
3. 验证Chrome API调用

### 问题3：Popup无法显示数据
**可能原因：**
- 数据获取失败
- 消息通信问题
- 渲染逻辑错误

**排查方法：**
1. 检查popup控制台错误
2. 验证Service Worker数据
3. 检查消息传递逻辑

### 问题4：操作无响应
**可能原因：**
- 权限不足
- API调用失败
- 事件绑定问题

**排查方法：**
1. 检查权限配置
2. 验证API调用结果
3. 检查事件监听器

## 测试完成标准

### 基础功能（必须通过）
- [x] 扩展成功加载
- [x] Service Worker正常运行
- [x] Popup界面正常显示
- [x] 数据正确获取和显示
- [x] 基本交互功能正常

### 高级功能（建议通过）
- [ ] 搜索功能完全正常
- [ ] 所有操作按钮正常工作
- [ ] 错误处理机制完善
- [ ] 性能表现良好

### 用户体验（优化目标）
- [ ] 界面美观易用
- [ ] 操作响应迅速
- [ ] 错误提示友好
- [ ] 功能直观易懂

## 测试报告模板

```
测试日期：____
测试环境：Chrome版本____ / 操作系统____
测试结果：

基础功能测试：
- 扩展加载：通过/失败
- Service Worker：通过/失败
- Popup显示：通过/失败
- 数据获取：通过/失败

交互功能测试：
- 标签页操作：通过/失败
- 窗口操作：通过/失败
- 搜索功能：通过/失败

发现的问题：
1. ____
2. ____

建议改进：
1. ____
2. ____

总体评价：____
```
