/**
 * 本地存储管理器
 * 封装Chrome Storage API，提供数据持久化功能
 */
export class LocalStorageManager {
  constructor() {
    this.storage = chrome.storage.local;
    this.syncStorage = chrome.storage.sync;
    this.compressionEnabled = true;
    this.encryptionEnabled = false;
    this.cache = new Map();
    this.cacheTimeout = 5 * 60 * 1000; // 5分钟缓存
  }

  /**
   * 存储数据
   */
  async set(key, value, options = {}) {
    try {
      const { 
        useSync = false, 
        compress = this.compressionEnabled,
        encrypt = this.encryptionEnabled,
        cache = true 
      } = options;

      let processedValue = value;

      // 数据压缩
      if (compress && typeof value === 'object') {
        processedValue = this.compress(JSON.stringify(value));
      }

      // 数据加密
      if (encrypt) {
        processedValue = await this.encrypt(processedValue);
      }

      const storage = useSync ? this.syncStorage : this.storage;
      const data = { [key]: processedValue };

      await storage.set(data);

      // 更新缓存
      if (cache) {
        this.cache.set(key, {
          value,
          timestamp: Date.now(),
          compressed: compress,
          encrypted: encrypt
        });
      }

      return true;
    } catch (error) {
      console.error('Storage set error:', error);
      throw new Error(`Failed to store data for key: ${key}`);
    }
  }

  /**
   * 获取数据
   */
  async get(key, defaultValue = null, options = {}) {
    try {
      const { 
        useSync = false, 
        useCache = true 
      } = options;

      // 检查缓存
      if (useCache && this.cache.has(key)) {
        const cached = this.cache.get(key);
        if (Date.now() - cached.timestamp < this.cacheTimeout) {
          return cached.value;
        } else {
          this.cache.delete(key);
        }
      }

      const storage = useSync ? this.syncStorage : this.storage;
      const result = await storage.get(key);
      
      if (!result.hasOwnProperty(key)) {
        return defaultValue;
      }

      let value = result[key];

      // 数据解密
      if (this.encryptionEnabled && typeof value === 'string' && value.startsWith('encrypted:')) {
        value = await this.decrypt(value);
      }

      // 数据解压缩
      if (this.compressionEnabled && typeof value === 'string' && value.startsWith('compressed:')) {
        value = JSON.parse(this.decompress(value));
      }

      // 更新缓存
      if (useCache) {
        this.cache.set(key, {
          value,
          timestamp: Date.now()
        });
      }

      return value;
    } catch (error) {
      console.error('Storage get error:', error);
      return defaultValue;
    }
  }

  /**
   * 删除数据
   */
  async remove(key, useSync = false) {
    try {
      const storage = useSync ? this.syncStorage : this.storage;
      await storage.remove(key);
      this.cache.delete(key);
      return true;
    } catch (error) {
      console.error('Storage remove error:', error);
      throw new Error(`Failed to remove data for key: ${key}`);
    }
  }

  /**
   * 清空存储
   */
  async clear(useSync = false) {
    try {
      const storage = useSync ? this.syncStorage : this.storage;
      await storage.clear();
      this.cache.clear();
      return true;
    } catch (error) {
      console.error('Storage clear error:', error);
      throw new Error('Failed to clear storage');
    }
  }

  /**
   * 获取所有键
   */
  async getAllKeys(useSync = false) {
    try {
      const storage = useSync ? this.syncStorage : this.storage;
      const result = await storage.get(null);
      return Object.keys(result);
    } catch (error) {
      console.error('Storage getAllKeys error:', error);
      return [];
    }
  }

  /**
   * 获取存储使用情况
   */
  async getUsage(useSync = false) {
    try {
      const storage = useSync ? this.syncStorage : this.storage;
      
      if (storage.getBytesInUse) {
        const bytesInUse = await storage.getBytesInUse();
        const quota = useSync ? chrome.storage.sync.QUOTA_BYTES : chrome.storage.local.QUOTA_BYTES;
        
        return {
          used: bytesInUse,
          total: quota,
          percentage: (bytesInUse / quota) * 100
        };
      }
      
      return { used: 0, total: 0, percentage: 0 };
    } catch (error) {
      console.error('Storage getUsage error:', error);
      return { used: 0, total: 0, percentage: 0 };
    }
  }

  /**
   * 批量操作
   */
  async batch(operations) {
    const results = [];
    
    for (const operation of operations) {
      try {
        let result;
        switch (operation.type) {
          case 'set':
            result = await this.set(operation.key, operation.value, operation.options);
            break;
          case 'get':
            result = await this.get(operation.key, operation.defaultValue, operation.options);
            break;
          case 'remove':
            result = await this.remove(operation.key, operation.useSync);
            break;
          default:
            throw new Error(`Unknown operation type: ${operation.type}`);
        }
        results.push({ success: true, result });
      } catch (error) {
        results.push({ success: false, error: error.message });
      }
    }
    
    return results;
  }

  /**
   * 数据压缩
   */
  compress(data) {
    try {
      // 简单的压缩实现，实际项目中可以使用更高效的压缩算法
      const compressed = btoa(unescape(encodeURIComponent(data)));
      return `compressed:${compressed}`;
    } catch (error) {
      console.warn('Compression failed:', error);
      return data;
    }
  }

  /**
   * 数据解压缩
   */
  decompress(compressedData) {
    try {
      if (!compressedData.startsWith('compressed:')) {
        return compressedData;
      }
      
      const compressed = compressedData.substring(11);
      return decodeURIComponent(escape(atob(compressed)));
    } catch (error) {
      console.warn('Decompression failed:', error);
      return compressedData;
    }
  }

  /**
   * 数据加密（简单实现）
   */
  async encrypt(data) {
    // 这里应该实现真正的加密算法
    // 为了演示，使用简单的base64编码
    try {
      const encrypted = btoa(JSON.stringify(data));
      return `encrypted:${encrypted}`;
    } catch (error) {
      console.warn('Encryption failed:', error);
      return data;
    }
  }

  /**
   * 数据解密
   */
  async decrypt(encryptedData) {
    try {
      if (!encryptedData.startsWith('encrypted:')) {
        return encryptedData;
      }
      
      const encrypted = encryptedData.substring(10);
      return JSON.parse(atob(encrypted));
    } catch (error) {
      console.warn('Decryption failed:', error);
      return encryptedData;
    }
  }

  /**
   * 清理缓存
   */
  clearCache() {
    this.cache.clear();
  }

  /**
   * 获取缓存统计
   */
  getCacheStats() {
    return {
      size: this.cache.size,
      keys: Array.from(this.cache.keys())
    };
  }

  /**
   * 监听存储变化
   */
  onChanged(callback) {
    chrome.storage.onChanged.addListener((changes, areaName) => {
      callback(changes, areaName);
    });
  }
}
