/**
 * Window数据模型
 * 管理浏览器窗口的所有属性和操作
 */
export class Window {
  constructor(data = {}) {
    this.id = data.id || null;
    this.focused = data.focused || false;
    this.top = data.top || 0;
    this.left = data.left || 0;
    this.width = data.width || 800;
    this.height = data.height || 600;
    this.incognito = data.incognito || false;
    this.type = data.type || 'normal'; // normal, popup, panel, app, devtools
    this.state = data.state || 'normal'; // normal, minimized, maximized, fullscreen
    this.alwaysOnTop = data.alwaysOnTop || false;
    this.sessionId = data.sessionId || null;
    
    // 扩展属性
    this.name = data.name || '';
    this.description = data.description || '';
    this.color = data.color || '#4285f4';
    this.tabs = data.tabs || [];
    this.tabGroups = data.tabGroups || [];
    this.createdAt = data.createdAt || new Date().toISOString();
    this.lastAccessed = data.lastAccessed || new Date().toISOString();
    this.isBookmarked = data.isBookmarked || false;
    this.customData = data.customData || {};
  }

  /**
   * 从Chrome Window对象创建Window实例
   */
  static fromChromeWindow(chromeWindow) {
    return new Window({
      id: chromeWindow.id,
      focused: chromeWindow.focused,
      top: chromeWindow.top,
      left: chromeWindow.left,
      width: chromeWindow.width,
      height: chromeWindow.height,
      incognito: chromeWindow.incognito,
      type: chromeWindow.type,
      state: chromeWindow.state,
      alwaysOnTop: chromeWindow.alwaysOnTop,
      sessionId: chromeWindow.sessionId,
      tabs: chromeWindow.tabs || []
    });
  }

  /**
   * 转换为Chrome Window格式
   */
  toChromeWindow() {
    return {
      id: this.id,
      focused: this.focused,
      top: this.top,
      left: this.left,
      width: this.width,
      height: this.height,
      incognito: this.incognito,
      type: this.type,
      state: this.state,
      alwaysOnTop: this.alwaysOnTop,
      sessionId: this.sessionId
    };
  }

  /**
   * 添加标签页
   */
  addTab(tab) {
    if (!this.tabs.find(t => t.id === tab.id)) {
      this.tabs.push(tab);
      tab.windowId = this.id;
    }
  }

  /**
   * 移除标签页
   */
  removeTab(tabId) {
    this.tabs = this.tabs.filter(tab => tab.id !== tabId);
  }

  /**
   * 获取活跃标签页
   */
  getActiveTab() {
    return this.tabs.find(tab => tab.active);
  }

  /**
   * 获取标签页数量
   */
  getTabCount() {
    return this.tabs.length;
  }

  /**
   * 获取固定标签页数量
   */
  getPinnedTabCount() {
    return this.tabs.filter(tab => tab.pinned).length;
  }

  /**
   * 验证数据有效性
   */
  validate() {
    const errors = [];
    
    if (this.width < 0 || this.height < 0) {
      errors.push('Width and height must be positive');
    }
    
    if (!['normal', 'popup', 'panel', 'app', 'devtools'].includes(this.type)) {
      errors.push('Invalid window type');
    }
    
    if (!['normal', 'minimized', 'maximized', 'fullscreen'].includes(this.state)) {
      errors.push('Invalid window state');
    }
    
    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * 序列化为JSON
   */
  toJSON() {
    return {
      id: this.id,
      focused: this.focused,
      top: this.top,
      left: this.left,
      width: this.width,
      height: this.height,
      incognito: this.incognito,
      type: this.type,
      state: this.state,
      alwaysOnTop: this.alwaysOnTop,
      sessionId: this.sessionId,
      name: this.name,
      description: this.description,
      color: this.color,
      tabs: this.tabs.map(tab => tab.toJSON ? tab.toJSON() : tab),
      tabGroups: this.tabGroups,
      createdAt: this.createdAt,
      lastAccessed: this.lastAccessed,
      isBookmarked: this.isBookmarked,
      customData: this.customData
    };
  }

  /**
   * 克隆窗口
   */
  clone() {
    return new Window(this.toJSON());
  }

  /**
   * 更新访问信息
   */
  updateAccess() {
    this.lastAccessed = new Date().toISOString();
  }

  /**
   * 检查是否匹配搜索条件
   */
  matches(searchTerm) {
    const term = searchTerm.toLowerCase();
    return this.name.toLowerCase().includes(term) ||
           this.description.toLowerCase().includes(term) ||
           this.tabs.some(tab => tab.matches && tab.matches(term));
  }

  /**
   * 获取窗口摘要信息
   */
  getSummary() {
    return {
      id: this.id,
      name: this.name || `Window ${this.id}`,
      tabCount: this.getTabCount(),
      pinnedTabCount: this.getPinnedTabCount(),
      state: this.state,
      incognito: this.incognito
    };
  }
}
