/**
 * Tab数据模型
 * 管理标签页的所有属性和操作
 */
export class Tab {
  constructor(data = {}) {
    this.id = data.id || null;
    this.url = data.url || '';
    this.title = data.title || '';
    this.favicon = data.favicon || '';
    this.windowId = data.windowId || null;
    this.index = data.index || 0;
    this.active = data.active || false;
    this.pinned = data.pinned || false;
    this.audible = data.audible || false;
    this.muted = data.muted || false;
    this.incognito = data.incognito || false;
    this.groupId = data.groupId || null;
    
    // 扩展属性
    this.notes = data.notes || '';
    this.tags = data.tags || [];
    this.category = data.category || '';
    this.createdAt = data.createdAt || new Date().toISOString();
    this.lastAccessed = data.lastAccessed || new Date().toISOString();
    this.accessCount = data.accessCount || 0;
    this.isBookmarked = data.isBookmarked || false;
    this.customData = data.customData || {};
  }

  /**
   * 从Chrome Tab对象创建Tab实例
   */
  static fromChromeTab(chromeTab) {
    return new Tab({
      id: chromeTab.id,
      url: chromeTab.url,
      title: chromeTab.title,
      favicon: chromeTab.favIconUrl,
      windowId: chromeTab.windowId,
      index: chromeTab.index,
      active: chromeTab.active,
      pinned: chromeTab.pinned,
      audible: chromeTab.audible,
      muted: chromeTab.mutedInfo?.muted || false,
      incognito: chromeTab.incognito,
      groupId: chromeTab.groupId
    });
  }

  /**
   * 转换为Chrome Tab格式
   */
  toChromeTab() {
    return {
      id: this.id,
      url: this.url,
      title: this.title,
      favIconUrl: this.favicon,
      windowId: this.windowId,
      index: this.index,
      active: this.active,
      pinned: this.pinned,
      audible: this.audible,
      mutedInfo: { muted: this.muted },
      incognito: this.incognito,
      groupId: this.groupId
    };
  }

  /**
   * 验证数据有效性
   */
  validate() {
    const errors = [];
    
    if (!this.url) {
      errors.push('URL is required');
    }
    
    if (!this.title) {
      errors.push('Title is required');
    }
    
    if (this.windowId !== null && typeof this.windowId !== 'number') {
      errors.push('WindowId must be a number or null');
    }
    
    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * 序列化为JSON
   */
  toJSON() {
    return {
      id: this.id,
      url: this.url,
      title: this.title,
      favicon: this.favicon,
      windowId: this.windowId,
      index: this.index,
      active: this.active,
      pinned: this.pinned,
      audible: this.audible,
      muted: this.muted,
      incognito: this.incognito,
      groupId: this.groupId,
      notes: this.notes,
      tags: this.tags,
      category: this.category,
      createdAt: this.createdAt,
      lastAccessed: this.lastAccessed,
      accessCount: this.accessCount,
      isBookmarked: this.isBookmarked,
      customData: this.customData
    };
  }

  /**
   * 克隆标签页
   */
  clone() {
    return new Tab(this.toJSON());
  }

  /**
   * 更新访问信息
   */
  updateAccess() {
    this.lastAccessed = new Date().toISOString();
    this.accessCount++;
  }

  /**
   * 获取域名
   */
  getDomain() {
    try {
      return new URL(this.url).hostname;
    } catch {
      return '';
    }
  }

  /**
   * 检查是否匹配搜索条件
   */
  matches(searchTerm) {
    const term = searchTerm.toLowerCase();
    return this.title.toLowerCase().includes(term) ||
           this.url.toLowerCase().includes(term) ||
           this.notes.toLowerCase().includes(term) ||
           this.tags.some(tag => tag.toLowerCase().includes(term));
  }
}
