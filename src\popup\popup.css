/* 基础样式重置和变量 */
:root {
  --primary-color: #4285f4;
  --secondary-color: #34a853;
  --danger-color: #ea4335;
  --warning-color: #fbbc04;
  --background-color: #ffffff;
  --surface-color: #f8f9fa;
  --border-color: #e0e0e0;
  --text-primary: #202124;
  --text-secondary: #5f6368;
  --text-disabled: #9aa0a6;
  --shadow-light: 0 1px 3px rgba(0,0,0,0.12);
  --shadow-medium: 0 4px 6px rgba(0,0,0,0.16);
  --shadow-heavy: 0 10px 20px rgba(0,0,0,0.19);
  --border-radius: 8px;
  --transition: all 0.2s ease;
}

/* 暗色主题 */
[data-theme="dark"] {
  --background-color: #202124;
  --surface-color: #303134;
  --border-color: #5f6368;
  --text-primary: #e8eaed;
  --text-secondary: #9aa0a6;
  --text-disabled: #5f6368;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  font-size: 14px;
  line-height: 1.5;
  color: var(--text-primary);
  background-color: var(--background-color);
  width: 400px;
  min-height: 500px;
  max-height: 600px;
  overflow: hidden;
}

/* 应用容器 */
.app {
  display: flex;
  flex-direction: column;
  height: 100vh;
  max-height: 600px;
}

/* 头部样式 */
.header {
  background-color: var(--surface-color);
  border-bottom: 1px solid var(--border-color);
  padding: 12px 16px;
  flex-shrink: 0;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.logo {
  display: flex;
  align-items: center;
  gap: 8px;
}

.logo-icon {
  width: 24px;
  height: 24px;
  color: var(--primary-color);
}

.logo-text {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
}

.header-actions {
  display: flex;
  gap: 4px;
}

/* 按钮样式 */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  padding: 8px 12px;
  border: none;
  border-radius: var(--border-radius);
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition);
  text-decoration: none;
  background: transparent;
  color: var(--text-primary);
}

.btn:hover {
  background-color: var(--border-color);
}

.btn:active {
  transform: scale(0.98);
}

.btn-icon {
  padding: 8px;
  border-radius: 50%;
}

.btn-icon svg {
  width: 20px;
  height: 20px;
}

.btn-primary {
  background-color: var(--primary-color);
  color: white;
}

.btn-primary:hover {
  background-color: #3367d6;
}

.btn-secondary {
  background-color: var(--surface-color);
  border: 1px solid var(--border-color);
}

.btn-secondary:hover {
  background-color: var(--border-color);
}

/* 搜索容器 */
.search-container {
  padding: 16px;
  background-color: var(--background-color);
  border-bottom: 1px solid var(--border-color);
  flex-shrink: 0;
}

.search-box {
  position: relative;
  display: flex;
  align-items: center;
  background-color: var(--surface-color);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: 8px 12px;
  margin-bottom: 12px;
}

.search-icon {
  width: 20px;
  height: 20px;
  color: var(--text-secondary);
  margin-right: 8px;
}

.search-input {
  flex: 1;
  border: none;
  background: transparent;
  font-size: 14px;
  color: var(--text-primary);
  outline: none;
}

.search-input::placeholder {
  color: var(--text-secondary);
}

.search-clear {
  opacity: 0;
  transition: var(--transition);
}

.search-clear.visible {
  opacity: 1;
}

.search-filters {
  display: flex;
  gap: 4px;
}

.filter-btn {
  padding: 6px 12px;
  border: none;
  background: transparent;
  color: var(--text-secondary);
  border-radius: 16px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition);
}

.filter-btn:hover {
  background-color: var(--border-color);
}

.filter-btn.active {
  background-color: var(--primary-color);
  color: white;
}

/* 主要内容区域 */
.main-content {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
}

/* 加载状态 */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid var(--border-color);
  border-top: 3px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;
}

.empty-icon {
  width: 48px;
  height: 48px;
  color: var(--text-disabled);
  margin-bottom: 16px;
}

.empty-state h3 {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 8px;
}

.empty-state p {
  color: var(--text-secondary);
  font-size: 14px;
}

/* 统计信息 */
.stats-container {
  display: flex;
  gap: 16px;
  margin-bottom: 20px;
  padding: 16px;
  background-color: var(--surface-color);
  border-radius: var(--border-radius);
  border: 1px solid var(--border-color);
}

.stat-item {
  flex: 1;
  text-align: center;
}

.stat-number {
  display: block;
  font-size: 24px;
  font-weight: 700;
  color: var(--primary-color);
  line-height: 1;
}

.stat-label {
  font-size: 12px;
  color: var(--text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* 窗口列表 */
.windows-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.window-item {
  background-color: var(--surface-color);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  overflow: hidden;
  transition: var(--transition);
}

.window-item:hover {
  box-shadow: var(--shadow-light);
}

.window-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background-color: var(--background-color);
  border-bottom: 1px solid var(--border-color);
  cursor: pointer;
}

.window-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.window-icon {
  width: 16px;
  height: 16px;
  color: var(--text-secondary);
}

.window-title {
  font-weight: 600;
  color: var(--text-primary);
}

.window-meta {
  font-size: 12px;
  color: var(--text-secondary);
}

.window-actions {
  display: flex;
  gap: 4px;
}

.window-tabs {
  padding: 8px;
}

.tab-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px;
  border-radius: 6px;
  cursor: pointer;
  transition: var(--transition);
}

.tab-item:hover {
  background-color: var(--border-color);
}

.tab-favicon {
  width: 16px;
  height: 16px;
  border-radius: 2px;
  flex-shrink: 0;
}

.tab-info {
  flex: 1;
  min-width: 0;
}

.tab-title {
  font-size: 13px;
  font-weight: 500;
  color: var(--text-primary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.tab-url {
  font-size: 11px;
  color: var(--text-secondary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.tab-actions {
  display: flex;
  gap: 2px;
  opacity: 0;
  transition: var(--transition);
}

.tab-item:hover .tab-actions {
  opacity: 1;
}

/* 底部操作栏 */
.footer {
  background-color: var(--surface-color);
  border-top: 1px solid var(--border-color);
  padding: 12px 16px;
  flex-shrink: 0;
}

.footer-actions {
  display: flex;
  gap: 8px;
}

.footer-actions .btn {
  flex: 1;
  justify-content: center;
}

/* 模态框 */
.modal-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal {
  background-color: var(--background-color);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-heavy);
  max-width: 90%;
  max-height: 90%;
  overflow: hidden;
}

.modal-header {
  padding: 16px 20px;
  border-bottom: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.modal-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
}

.modal-body {
  padding: 20px;
  overflow-y: auto;
}

.modal-footer {
  padding: 16px 20px;
  border-top: 1px solid var(--border-color);
  display: flex;
  gap: 8px;
  justify-content: flex-end;
}

/* 通知 */
.notification-container {
  position: fixed;
  top: 16px;
  right: 16px;
  z-index: 1100;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.notification {
  background-color: var(--background-color);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: 12px 16px;
  box-shadow: var(--shadow-medium);
  min-width: 300px;
  animation: slideIn 0.3s ease;
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

.notification.success {
  border-left: 4px solid var(--secondary-color);
}

.notification.error {
  border-left: 4px solid var(--danger-color);
}

.notification.warning {
  border-left: 4px solid var(--warning-color);
}

/* 上下文菜单 */
.context-menu {
  position: fixed;
  background-color: var(--background-color);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-medium);
  padding: 4px 0;
  min-width: 150px;
  z-index: 1200;
}

.context-menu-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  cursor: pointer;
  font-size: 13px;
  color: var(--text-primary);
  transition: var(--transition);
}

.context-menu-item:hover {
  background-color: var(--border-color);
}

.context-menu-item svg {
  width: 16px;
  height: 16px;
  color: var(--text-secondary);
}

/* 响应式设计 */
@media (max-width: 480px) {
  body {
    width: 100vw;
  }
  
  .header-content {
    padding: 8px 12px;
  }
  
  .search-container {
    padding: 12px;
  }
  
  .main-content {
    padding: 12px;
  }
  
  .footer {
    padding: 8px 12px;
  }
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: var(--surface-color);
}

::-webkit-scrollbar-thumb {
  background: var(--border-color);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--text-secondary);
}
