# Bookmark Tab Manager - Chrome Extension

一个强大的书签式标签页管理系统Chrome扩展，提供高级标签页管理功能。

## 功能特性

### 已实现功能（子任务1）
- ✅ **项目基础架构**：完整的Manifest V3配置和目录结构
- ✅ **数据模型系统**：Tab、Window、TabGroup、Settings数据模型
- ✅ **本地存储管理**：LocalStorageManager，支持数据压缩和缓存
- ✅ **Chrome API封装**：ChromeAPIWrapper，统一的API接口和错误处理
- ✅ **Service Worker**：后台服务，自动数据同步和事件监听
- ✅ **基础用户界面**：现代化的popup界面，支持搜索和筛选
- ✅ **标签页管理**：查看、激活、关闭标签页
- ✅ **窗口管理**：查看、聚焦、关闭窗口
- ✅ **实时搜索**：支持标题、URL、备注搜索
- ✅ **数据统计**：显示标签页、窗口、分组数量

### 计划功能（后续子任务）
- 🔄 **高级搜索筛选**：多条件组合筛选、域名筛选、分类筛选
- 🔄 **导入导出管理**：JSON/HTML格式导出，数据备份恢复
- 🔄 **拖拽排序管理**：标签页拖拽重排，跨窗口移动
- 🔄 **撤销管理系统**：操作历史记录，快速撤销功能
- 🔄 **重复标签页管理**：智能检测和处理重复标签页
- 🔄 **响应式设计优化**：完整的动画效果和用户体验优化

## 安装方法

### 开发者模式安装

1. **下载源码**
   ```bash
   git clone <repository-url>
   cd bookmark-tab-manager
   ```

2. **打开Chrome扩展管理页面**
   - 在Chrome地址栏输入：`chrome://extensions/`
   - 或者：菜单 → 更多工具 → 扩展程序

3. **启用开发者模式**
   - 点击右上角的"开发者模式"开关

4. **加载扩展**
   - 点击"加载已解压的扩展程序"
   - 选择项目根目录（包含manifest.json的文件夹）

5. **验证安装**
   - 扩展应该出现在扩展列表中
   - 工具栏应该显示扩展图标

## 使用方法

### 基础操作

1. **打开管理界面**
   - 点击工具栏中的扩展图标
   - 或使用快捷键（如果已配置）

2. **搜索标签页**
   - 在搜索框中输入关键词
   - 支持搜索标题、URL、备注内容
   - 使用筛选按钮过滤结果类型

3. **管理标签页**
   - 点击标签页项目激活标签页
   - 点击关闭按钮关闭标签页
   - 右键菜单提供更多操作选项

4. **管理窗口**
   - 点击窗口标题展开/折叠标签页列表
   - 使用聚焦按钮切换到指定窗口
   - 通过菜单创建新标签页或关闭窗口

### 高级功能

1. **数据同步**
   - 点击同步按钮手动同步浏览器数据
   - 扩展会自动定期同步数据

2. **统计信息**
   - 界面顶部显示标签页、窗口、分组统计
   - 实时更新数据状态

## 项目结构

```
bookmark-tab-manager/
├── manifest.json                 # Chrome扩展配置文件
├── src/
│   ├── models/                   # 数据模型
│   │   ├── Tab.js               # 标签页数据模型
│   │   ├── Window.js            # 窗口数据模型
│   │   ├── TabGroup.js          # 标签组数据模型
│   │   └── Settings.js          # 设置数据模型
│   ├── storage/                  # 存储管理
│   │   └── LocalStorageManager.js
│   ├── api/                      # API封装
│   │   └── ChromeAPIWrapper.js
│   ├── background/               # 后台脚本
│   │   └── service-worker.js
│   └── popup/                    # 弹窗界面
│       ├── popup.html
│       ├── popup.css
│       └── popup.js
├── assets/                       # 静态资源
│   └── icons/                    # 图标文件
└── README.md                     # 项目说明
```

## 技术栈

- **前端**：原生JavaScript (ES6+)、HTML5、CSS3
- **架构**：Manifest V3、Service Worker
- **存储**：Chrome Storage API
- **样式**：CSS Grid + Flexbox、CSS变量系统
- **图标**：SVG图标系统

## 开发说明

### 核心组件

1. **数据模型层**
   - 提供统一的数据结构和验证
   - 支持序列化和反序列化
   - 包含业务逻辑方法

2. **存储管理层**
   - 封装Chrome Storage API
   - 支持数据压缩和缓存
   - 提供批量操作接口

3. **API封装层**
   - Promise化Chrome API
   - 统一错误处理机制
   - 事件监听管理

4. **用户界面层**
   - 响应式设计
   - 实时搜索和筛选
   - 现代化交互体验

### 测试验收

#### 基础功能测试
1. **扩展加载测试**
   - [ ] 扩展可以成功加载到Chrome
   - [ ] 没有控制台错误信息
   - [ ] 扩展图标正常显示

2. **数据获取测试**
   - [ ] 可以获取当前浏览器标签页
   - [ ] 可以获取窗口信息
   - [ ] 数据正确存储到本地

3. **界面功能测试**
   - [ ] Popup界面正常显示
   - [ ] 搜索功能正常工作
   - [ ] 筛选功能正常工作
   - [ ] 统计信息正确显示

4. **标签页操作测试**
   - [ ] 可以激活指定标签页
   - [ ] 可以关闭标签页
   - [ ] 操作后界面正确更新

5. **窗口操作测试**
   - [ ] 可以聚焦指定窗口
   - [ ] 可以创建新窗口
   - [ ] 可以关闭窗口

#### 性能测试
- [ ] 大量标签页（100+）时响应速度
- [ ] 内存使用情况
- [ ] 数据同步效率

## 故障排除

### 常见问题

1. **扩展无法加载**
   - 检查manifest.json语法是否正确
   - 确认所有引用的文件都存在
   - 查看Chrome扩展管理页面的错误信息

2. **数据无法显示**
   - 检查Service Worker是否正常运行
   - 查看浏览器控制台错误信息
   - 确认Chrome API权限是否正确配置

3. **界面显示异常**
   - 检查CSS文件是否正确加载
   - 确认HTML结构是否完整
   - 查看控制台JavaScript错误

### 调试方法

1. **Service Worker调试**
   - 在chrome://extensions/页面点击"Service Worker"链接
   - 查看控制台日志和错误信息

2. **Popup调试**
   - 右键点击扩展图标选择"检查弹出式窗口"
   - 使用开发者工具调试界面和脚本

3. **存储数据检查**
   - 在开发者工具中查看Chrome Storage
   - 验证数据结构和内容是否正确

## 贡献指南

1. Fork项目仓库
2. 创建功能分支
3. 提交代码更改
4. 创建Pull Request

## 许可证

MIT License

## 更新日志

### v1.0.0 (当前版本)
- ✅ 完成基础架构和核心功能
- ✅ 实现标签页和窗口管理
- ✅ 添加搜索和筛选功能
- ✅ 完成数据存储和同步

### 计划更新
- v1.1.0: 高级搜索和导入导出功能
- v1.2.0: 拖拽排序和撤销管理
- v1.3.0: 重复标签页管理和性能优化
- v2.0.0: 完整用户体验优化和高级功能
