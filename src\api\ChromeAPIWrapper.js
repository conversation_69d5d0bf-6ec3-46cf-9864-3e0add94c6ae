/**
 * Chrome API封装器
 * 提供统一的Chrome API接口和错误处理
 */
export class ChromeAPIWrapper {
  constructor() {
    this.eventListeners = new Map();
    this.initialized = false;
    this.init();
  }

  /**
   * 初始化API包装器
   */
  async init() {
    try {
      // 检查Chrome API可用性
      if (!chrome || !chrome.tabs || !chrome.windows) {
        throw new Error('Chrome APIs not available');
      }
      
      this.initialized = true;
      console.log('ChromeAPIWrapper initialized successfully');
    } catch (error) {
      console.error('ChromeAPIWrapper initialization failed:', error);
      throw error;
    }
  }

  /**
   * 标签页操作 - 查询标签页
   */
  async queryTabs(queryInfo = {}) {
    try {
      return await chrome.tabs.query(queryInfo);
    } catch (error) {
      console.error('Query tabs error:', error);
      throw new Error(`Failed to query tabs: ${error.message}`);
    }
  }

  /**
   * 标签页操作 - 创建标签页
   */
  async createTab(createProperties) {
    try {
      return await chrome.tabs.create(createProperties);
    } catch (error) {
      console.error('Create tab error:', error);
      throw new Error(`Failed to create tab: ${error.message}`);
    }
  }

  /**
   * 标签页操作 - 更新标签页
   */
  async updateTab(tabId, updateProperties) {
    try {
      return await chrome.tabs.update(tabId, updateProperties);
    } catch (error) {
      console.error('Update tab error:', error);
      throw new Error(`Failed to update tab ${tabId}: ${error.message}`);
    }
  }

  /**
   * 标签页操作 - 移除标签页
   */
  async removeTabs(tabIds) {
    try {
      const ids = Array.isArray(tabIds) ? tabIds : [tabIds];
      return await chrome.tabs.remove(ids);
    } catch (error) {
      console.error('Remove tabs error:', error);
      throw new Error(`Failed to remove tabs: ${error.message}`);
    }
  }

  /**
   * 标签页操作 - 移动标签页
   */
  async moveTabs(tabIds, moveProperties) {
    try {
      const ids = Array.isArray(tabIds) ? tabIds : [tabIds];
      return await chrome.tabs.move(ids, moveProperties);
    } catch (error) {
      console.error('Move tabs error:', error);
      throw new Error(`Failed to move tabs: ${error.message}`);
    }
  }

  /**
   * 标签页操作 - 激活标签页
   */
  async activateTab(tabId) {
    try {
      return await chrome.tabs.update(tabId, { active: true });
    } catch (error) {
      console.error('Activate tab error:', error);
      throw new Error(`Failed to activate tab ${tabId}: ${error.message}`);
    }
  }

  /**
   * 标签页操作 - 固定/取消固定标签页
   */
  async pinTab(tabId, pinned = true) {
    try {
      return await chrome.tabs.update(tabId, { pinned });
    } catch (error) {
      console.error('Pin tab error:', error);
      throw new Error(`Failed to pin tab ${tabId}: ${error.message}`);
    }
  }

  /**
   * 标签页操作 - 静音/取消静音标签页
   */
  async muteTab(tabId, muted = true) {
    try {
      return await chrome.tabs.update(tabId, { muted });
    } catch (error) {
      console.error('Mute tab error:', error);
      throw new Error(`Failed to mute tab ${tabId}: ${error.message}`);
    }
  }

  /**
   * 窗口操作 - 获取所有窗口
   */
  async getAllWindows(getInfo = {}) {
    try {
      const defaultGetInfo = { populate: true, windowTypes: ['normal'] };
      return await chrome.windows.getAll({ ...defaultGetInfo, ...getInfo });
    } catch (error) {
      console.error('Get all windows error:', error);
      throw new Error(`Failed to get windows: ${error.message}`);
    }
  }

  /**
   * 窗口操作 - 获取当前窗口
   */
  async getCurrentWindow(getInfo = {}) {
    try {
      const defaultGetInfo = { populate: true };
      return await chrome.windows.getCurrent({ ...defaultGetInfo, ...getInfo });
    } catch (error) {
      console.error('Get current window error:', error);
      throw new Error(`Failed to get current window: ${error.message}`);
    }
  }

  /**
   * 窗口操作 - 创建窗口
   */
  async createWindow(createData) {
    try {
      return await chrome.windows.create(createData);
    } catch (error) {
      console.error('Create window error:', error);
      throw new Error(`Failed to create window: ${error.message}`);
    }
  }

  /**
   * 窗口操作 - 更新窗口
   */
  async updateWindow(windowId, updateInfo) {
    try {
      return await chrome.windows.update(windowId, updateInfo);
    } catch (error) {
      console.error('Update window error:', error);
      throw new Error(`Failed to update window ${windowId}: ${error.message}`);
    }
  }

  /**
   * 窗口操作 - 移除窗口
   */
  async removeWindow(windowId) {
    try {
      return await chrome.windows.remove(windowId);
    } catch (error) {
      console.error('Remove window error:', error);
      throw new Error(`Failed to remove window ${windowId}: ${error.message}`);
    }
  }

  /**
   * 标签组操作 - 查询标签组
   */
  async queryTabGroups(queryInfo = {}) {
    try {
      if (chrome.tabGroups && chrome.tabGroups.query) {
        return await chrome.tabGroups.query(queryInfo);
      }
      return [];
    } catch (error) {
      console.error('Query tab groups error:', error);
      return [];
    }
  }

  /**
   * 标签组操作 - 创建标签组
   */
  async createTabGroup(createProperties) {
    try {
      if (chrome.tabGroups && chrome.tabGroups.group) {
        return await chrome.tabGroups.group(createProperties);
      }
      throw new Error('Tab groups not supported');
    } catch (error) {
      console.error('Create tab group error:', error);
      throw new Error(`Failed to create tab group: ${error.message}`);
    }
  }

  /**
   * 标签组操作 - 更新标签组
   */
  async updateTabGroup(groupId, updateProperties) {
    try {
      if (chrome.tabGroups && chrome.tabGroups.update) {
        return await chrome.tabGroups.update(groupId, updateProperties);
      }
      throw new Error('Tab groups not supported');
    } catch (error) {
      console.error('Update tab group error:', error);
      throw new Error(`Failed to update tab group ${groupId}: ${error.message}`);
    }
  }

  /**
   * 事件监听 - 添加标签页事件监听器
   */
  addTabEventListeners() {
    const events = ['onCreated', 'onUpdated', 'onMoved', 'onRemoved', 'onActivated'];
    
    events.forEach(eventName => {
      if (chrome.tabs[eventName]) {
        const listener = (...args) => {
          this.emit(`tab.${eventName.substring(2).toLowerCase()}`, ...args);
        };
        chrome.tabs[eventName].addListener(listener);
        this.eventListeners.set(`tab.${eventName}`, listener);
      }
    });
  }

  /**
   * 事件监听 - 添加窗口事件监听器
   */
  addWindowEventListeners() {
    const events = ['onCreated', 'onRemoved', 'onFocusChanged'];
    
    events.forEach(eventName => {
      if (chrome.windows[eventName]) {
        const listener = (...args) => {
          this.emit(`window.${eventName.substring(2).toLowerCase()}`, ...args);
        };
        chrome.windows[eventName].addListener(listener);
        this.eventListeners.set(`window.${eventName}`, listener);
      }
    });
  }

  /**
   * 事件发射器
   */
  emit(eventName, ...args) {
    if (this.eventListeners.has(eventName)) {
      const listeners = this.eventListeners.get(eventName);
      if (Array.isArray(listeners)) {
        listeners.forEach(listener => listener(...args));
      } else {
        listeners(...args);
      }
    }
  }

  /**
   * 添加事件监听器
   */
  on(eventName, listener) {
    if (!this.eventListeners.has(eventName)) {
      this.eventListeners.set(eventName, []);
    }
    
    const listeners = this.eventListeners.get(eventName);
    if (Array.isArray(listeners)) {
      listeners.push(listener);
    } else {
      this.eventListeners.set(eventName, [listeners, listener]);
    }
  }

  /**
   * 移除事件监听器
   */
  off(eventName, listener) {
    if (this.eventListeners.has(eventName)) {
      const listeners = this.eventListeners.get(eventName);
      if (Array.isArray(listeners)) {
        const index = listeners.indexOf(listener);
        if (index > -1) {
          listeners.splice(index, 1);
        }
      } else if (listeners === listener) {
        this.eventListeners.delete(eventName);
      }
    }
  }

  /**
   * 获取扩展信息
   */
  getExtensionInfo() {
    return {
      id: chrome.runtime.id,
      version: chrome.runtime.getManifest().version,
      name: chrome.runtime.getManifest().name
    };
  }

  /**
   * 检查权限
   */
  async checkPermissions(permissions) {
    try {
      return await chrome.permissions.contains({ permissions });
    } catch (error) {
      console.error('Check permissions error:', error);
      return false;
    }
  }
}
