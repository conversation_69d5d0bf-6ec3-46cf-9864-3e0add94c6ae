/**
 * Popup主界面控制器
 * 管理用户界面交互和数据显示
 */

class PopupController {
  constructor() {
    this.browserData = null;
    this.filteredData = null;
    this.currentFilter = 'all';
    this.searchTerm = '';
    this.isLoading = true;
    
    this.init();
  }

  /**
   * 初始化控制器
   */
  async init() {
    try {
      console.log('Initializing popup controller...');
      
      // 绑定事件监听器
      this.bindEventListeners();
      
      // 加载浏览器数据
      await this.loadBrowserData();
      
      // 渲染界面
      this.render();
      
      console.log('Popup controller initialized successfully');
    } catch (error) {
      console.error('Popup controller initialization failed:', error);
      this.showError('Failed to initialize tab manager');
    }
  }

  /**
   * 绑定事件监听器
   */
  bindEventListeners() {
    // 搜索功能
    const searchInput = document.getElementById('searchInput');
    const clearSearchBtn = document.getElementById('clearSearchBtn');
    
    searchInput?.addEventListener('input', (e) => {
      this.handleSearch(e.target.value);
    });
    
    clearSearchBtn?.addEventListener('click', () => {
      this.clearSearch();
    });

    // 筛选按钮
    const filterButtons = document.querySelectorAll('.filter-btn');
    filterButtons.forEach(btn => {
      btn.addEventListener('click', (e) => {
        this.handleFilter(e.target.dataset.filter);
      });
    });

    // 头部操作按钮
    const syncBtn = document.getElementById('syncBtn');
    const settingsBtn = document.getElementById('settingsBtn');
    
    syncBtn?.addEventListener('click', () => {
      this.handleSync();
    });
    
    settingsBtn?.addEventListener('click', () => {
      this.handleSettings();
    });

    // 底部操作按钮
    const newWindowBtn = document.getElementById('newWindowBtn');
    const exportBtn = document.getElementById('exportBtn');
    const importBtn = document.getElementById('importBtn');
    
    newWindowBtn?.addEventListener('click', () => {
      this.handleNewWindow();
    });
    
    exportBtn?.addEventListener('click', () => {
      this.handleExport();
    });
    
    importBtn?.addEventListener('click', () => {
      this.handleImport();
    });

    // 键盘快捷键
    document.addEventListener('keydown', (e) => {
      this.handleKeyboard(e);
    });

    // 点击外部关闭上下文菜单
    document.addEventListener('click', (e) => {
      this.hideContextMenu();
    });
  }

  /**
   * 加载浏览器数据
   */
  async loadBrowserData() {
    try {
      this.showLoading(true);
      
      // 从Service Worker获取数据
      const response = await this.sendMessage({
        action: 'GET_BROWSER_DATA'
      });
      
      if (response.success) {
        this.browserData = response.data;
        this.filteredData = this.browserData;
        console.log('Browser data loaded:', this.browserData);
      } else {
        throw new Error(response.error || 'Failed to load browser data');
      }
    } catch (error) {
      console.error('Failed to load browser data:', error);
      this.showError('Failed to load browser data');
    } finally {
      this.showLoading(false);
    }
  }

  /**
   * 发送消息到Service Worker
   */
  async sendMessage(message) {
    return new Promise((resolve) => {
      chrome.runtime.sendMessage(message, (response) => {
        resolve(response || { success: false, error: 'No response' });
      });
    });
  }

  /**
   * 处理搜索
   */
  handleSearch(searchTerm) {
    this.searchTerm = searchTerm.toLowerCase().trim();
    this.updateSearchUI();
    this.filterData();
    this.render();
  }

  /**
   * 清除搜索
   */
  clearSearch() {
    const searchInput = document.getElementById('searchInput');
    if (searchInput) {
      searchInput.value = '';
      this.handleSearch('');
    }
  }

  /**
   * 更新搜索UI
   */
  updateSearchUI() {
    const clearBtn = document.getElementById('clearSearchBtn');
    if (clearBtn) {
      clearBtn.classList.toggle('visible', this.searchTerm.length > 0);
    }
  }

  /**
   * 处理筛选
   */
  handleFilter(filter) {
    this.currentFilter = filter;
    
    // 更新筛选按钮状态
    document.querySelectorAll('.filter-btn').forEach(btn => {
      btn.classList.toggle('active', btn.dataset.filter === filter);
    });
    
    this.filterData();
    this.render();
  }

  /**
   * 筛选数据
   */
  filterData() {
    if (!this.browserData) {
      this.filteredData = null;
      return;
    }

    let filteredWindows = {};
    let filteredTabs = {};
    let filteredGroups = {};

    // 应用搜索筛选
    Object.entries(this.browserData.windows || {}).forEach(([windowId, window]) => {
      const windowMatches = this.matchesSearch(window, 'window');
      const windowTabs = {};
      let hasMatchingTabs = false;

      // 筛选窗口中的标签页
      Object.entries(this.browserData.tabs || {}).forEach(([tabId, tab]) => {
        if (tab.windowId == windowId) {
          const tabMatches = this.matchesSearch(tab, 'tab');
          if (tabMatches) {
            windowTabs[tabId] = tab;
            filteredTabs[tabId] = tab;
            hasMatchingTabs = true;
          }
        }
      });

      // 如果窗口或其标签页匹配，则包含该窗口
      if (windowMatches || hasMatchingTabs) {
        filteredWindows[windowId] = {
          ...window,
          tabs: windowTabs
        };
      }
    });

    // 筛选标签组
    Object.entries(this.browserData.tabGroups || {}).forEach(([groupId, group]) => {
      if (this.matchesSearch(group, 'group')) {
        filteredGroups[groupId] = group;
      }
    });

    // 应用类型筛选
    if (this.currentFilter !== 'all') {
      if (this.currentFilter === 'tabs') {
        filteredWindows = {};
        filteredGroups = {};
      } else if (this.currentFilter === 'windows') {
        filteredTabs = {};
        filteredGroups = {};
      } else if (this.currentFilter === 'groups') {
        filteredWindows = {};
        filteredTabs = {};
      }
    }

    this.filteredData = {
      windows: filteredWindows,
      tabs: filteredTabs,
      tabGroups: filteredGroups,
      settings: this.browserData.settings
    };
  }

  /**
   * 检查项目是否匹配搜索条件
   */
  matchesSearch(item, type) {
    if (!this.searchTerm) return true;

    const searchFields = [];

    if (type === 'tab') {
      searchFields.push(
        item.title || '',
        item.url || '',
        item.notes || '',
        ...(item.tags || [])
      );
    } else if (type === 'window') {
      searchFields.push(
        item.name || '',
        item.description || ''
      );
    } else if (type === 'group') {
      searchFields.push(
        item.title || '',
        item.description || '',
        ...(item.tags || [])
      );
    }

    return searchFields.some(field =>
      field.toLowerCase().includes(this.searchTerm)
    );
  }

  /**
   * 渲染界面
   */
  render() {
    this.renderStats();
    this.renderContent();
  }

  /**
   * 渲染统计信息
   */
  renderStats() {
    const statsContainer = document.getElementById('statsContainer');
    const totalTabsEl = document.getElementById('totalTabs');
    const totalWindowsEl = document.getElementById('totalWindows');
    const totalGroupsEl = document.getElementById('totalGroups');

    if (!this.filteredData) {
      statsContainer.style.display = 'none';
      return;
    }

    const tabCount = Object.keys(this.filteredData.tabs || {}).length;
    const windowCount = Object.keys(this.filteredData.windows || {}).length;
    const groupCount = Object.keys(this.filteredData.tabGroups || {}).length;

    totalTabsEl.textContent = tabCount;
    totalWindowsEl.textContent = windowCount;
    totalGroupsEl.textContent = groupCount;

    statsContainer.style.display = tabCount > 0 || windowCount > 0 || groupCount > 0 ? 'flex' : 'none';
  }

  /**
   * 渲染主要内容
   */
  renderContent() {
    const windowsList = document.getElementById('windowsList');
    const emptyState = document.getElementById('emptyState');

    if (!this.filteredData || Object.keys(this.filteredData.windows || {}).length === 0) {
      windowsList.style.display = 'none';
      emptyState.style.display = 'flex';
      return;
    }

    emptyState.style.display = 'none';
    windowsList.style.display = 'block';
    windowsList.innerHTML = '';

    // 渲染每个窗口
    Object.entries(this.filteredData.windows).forEach(([windowId, window]) => {
      const windowElement = this.createWindowElement(windowId, window);
      windowsList.appendChild(windowElement);
    });
  }

  /**
   * 创建窗口元素
   */
  createWindowElement(windowId, window) {
    const windowDiv = document.createElement('div');
    windowDiv.className = 'window-item';
    windowDiv.dataset.windowId = windowId;

    const tabCount = Object.keys(window.tabs || {}).length;
    const windowTitle = window.name || `Window ${windowId}`;
    const windowMeta = `${tabCount} tabs`;

    windowDiv.innerHTML = `
      <div class="window-header" onclick="popupController.toggleWindow('${windowId}')">
        <div class="window-info">
          <svg class="window-icon" viewBox="0 0 24 24" fill="currentColor">
            <path d="M4 6H2v14c0 1.1.9 2 2 2h14v-2H4V6zm16-4H8c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm-1 9H9V9h10v2zm-4 4H9v-2h6v2zm4-8H9V5h10v2z"/>
          </svg>
          <div>
            <div class="window-title">${this.escapeHtml(windowTitle)}</div>
            <div class="window-meta">${windowMeta}</div>
          </div>
        </div>
        <div class="window-actions">
          <button class="btn btn-icon" onclick="event.stopPropagation(); popupController.focusWindow('${windowId}')" title="Focus Window">
            <svg viewBox="0 0 24 24" fill="currentColor">
              <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
            </svg>
          </button>
          <button class="btn btn-icon" onclick="event.stopPropagation(); popupController.showWindowMenu('${windowId}', event)" title="More Options">
            <svg viewBox="0 0 24 24" fill="currentColor">
              <path d="M12 8c1.1 0 2-.9 2-2s-.9-2-2-2-2 .9-2 2 .9 2 2 2zm0 2c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zm0 6c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2z"/>
            </svg>
          </button>
        </div>
      </div>
      <div class="window-tabs" id="windowTabs-${windowId}" style="display: none;">
        ${this.createTabsHTML(window.tabs || {})}
      </div>
    `;

    return windowDiv;
  }

  /**
   * 创建标签页HTML
   */
  createTabsHTML(tabs) {
    return Object.entries(tabs).map(([tabId, tab]) => {
      const favicon = tab.favicon || 'data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path fill="%23666" d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2z"/></svg>';

      return `
        <div class="tab-item" data-tab-id="${tabId}" onclick="popupController.activateTab('${tabId}')">
          <img class="tab-favicon" src="${favicon}" alt="" onerror="this.src='data:image/svg+xml,<svg xmlns=\\"http://www.w3.org/2000/svg\\" viewBox=\\"0 0 24 24\\"><path fill=\\"%23666\\" d=\\"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2z\\"/></svg>'">
          <div class="tab-info">
            <div class="tab-title">${this.escapeHtml(tab.title || 'Untitled')}</div>
            <div class="tab-url">${this.escapeHtml(this.formatUrl(tab.url || ''))}</div>
          </div>
          <div class="tab-actions">
            <button class="btn btn-icon" onclick="event.stopPropagation(); popupController.closeTab('${tabId}')" title="Close Tab">
              <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
              </svg>
            </button>
          </div>
        </div>
      `;
    }).join('');
  }

  /**
   * 显示/隐藏加载状态
   */
  showLoading(show) {
    const loadingState = document.getElementById('loadingState');
    const mainContent = document.querySelector('.main-content');

    if (show) {
      loadingState.style.display = 'flex';
      mainContent.style.overflow = 'hidden';
    } else {
      loadingState.style.display = 'none';
      mainContent.style.overflow = 'auto';
    }

    this.isLoading = show;
  }

  /**
   * 显示错误信息
   */
  showError(message) {
    this.showNotification(message, 'error');
  }

  /**
   * 显示通知
   */
  showNotification(message, type = 'info') {
    const container = document.getElementById('notificationContainer');
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.textContent = message;

    container.appendChild(notification);

    // 自动移除通知
    setTimeout(() => {
      if (notification.parentNode) {
        notification.parentNode.removeChild(notification);
      }
    }, 5000);
  }

  /**
   * 转义HTML
   */
  escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
  }

  /**
   * 格式化URL显示
   */
  formatUrl(url) {
    try {
      const urlObj = new URL(url);
      return urlObj.hostname + urlObj.pathname;
    } catch {
      return url;
    }
  }

  // 事件处理方法

  /**
   * 切换窗口展开/折叠
   */
  toggleWindow(windowId) {
    const tabsContainer = document.getElementById(`windowTabs-${windowId}`);
    if (tabsContainer) {
      const isVisible = tabsContainer.style.display !== 'none';
      tabsContainer.style.display = isVisible ? 'none' : 'block';
    }
  }

  /**
   * 激活标签页
   */
  async activateTab(tabId) {
    try {
      const response = await this.sendMessage({
        action: 'ACTIVATE_TAB',
        data: { tabId: parseInt(tabId) }
      });

      if (response.success) {
        this.showNotification('Tab activated', 'success');
        // 关闭popup
        window.close();
      } else {
        this.showError('Failed to activate tab');
      }
    } catch (error) {
      console.error('Failed to activate tab:', error);
      this.showError('Failed to activate tab');
    }
  }

  /**
   * 关闭标签页
   */
  async closeTab(tabId) {
    try {
      const response = await this.sendMessage({
        action: 'CLOSE_TABS',
        data: { tabIds: [parseInt(tabId)] }
      });

      if (response.success) {
        this.showNotification('Tab closed', 'success');
        // 重新加载数据
        await this.loadBrowserData();
        this.render();
      } else {
        this.showError('Failed to close tab');
      }
    } catch (error) {
      console.error('Failed to close tab:', error);
      this.showError('Failed to close tab');
    }
  }

  /**
   * 聚焦窗口
   */
  async focusWindow(windowId) {
    try {
      const response = await this.sendMessage({
        action: 'FOCUS_WINDOW',
        data: { windowId: parseInt(windowId) }
      });

      if (response.success) {
        this.showNotification('Window focused', 'success');
        window.close();
      } else {
        this.showError('Failed to focus window');
      }
    } catch (error) {
      console.error('Failed to focus window:', error);
      this.showError('Failed to focus window');
    }
  }

  /**
   * 显示窗口菜单
   */
  showWindowMenu(windowId, event) {
    event.preventDefault();
    event.stopPropagation();

    const contextMenu = document.getElementById('contextMenu');
    contextMenu.innerHTML = `
      <div class="context-menu-item" onclick="popupController.newTabInWindow('${windowId}')">
        <svg viewBox="0 0 24 24" fill="currentColor">
          <path d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z"/>
        </svg>
        New Tab
      </div>
      <div class="context-menu-item" onclick="popupController.closeWindow('${windowId}')">
        <svg viewBox="0 0 24 24" fill="currentColor">
          <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
        </svg>
        Close Window
      </div>
    `;

    contextMenu.style.display = 'block';
    contextMenu.style.left = event.pageX + 'px';
    contextMenu.style.top = event.pageY + 'px';
  }

  /**
   * 隐藏上下文菜单
   */
  hideContextMenu() {
    const contextMenu = document.getElementById('contextMenu');
    contextMenu.style.display = 'none';
  }

  /**
   * 在窗口中创建新标签页
   */
  async newTabInWindow(windowId) {
    try {
      const response = await this.sendMessage({
        action: 'CREATE_TAB',
        data: {
          windowId: parseInt(windowId),
          url: 'chrome://newtab/'
        }
      });

      if (response.success) {
        this.showNotification('New tab created', 'success');
        await this.loadBrowserData();
        this.render();
      } else {
        this.showError('Failed to create new tab');
      }
    } catch (error) {
      console.error('Failed to create new tab:', error);
      this.showError('Failed to create new tab');
    }

    this.hideContextMenu();
  }

  /**
   * 关闭窗口
   */
  async closeWindow(windowId) {
    try {
      const response = await this.sendMessage({
        action: 'CLOSE_WINDOW',
        data: { windowId: parseInt(windowId) }
      });

      if (response.success) {
        this.showNotification('Window closed', 'success');
        await this.loadBrowserData();
        this.render();
      } else {
        this.showError('Failed to close window');
      }
    } catch (error) {
      console.error('Failed to close window:', error);
      this.showError('Failed to close window');
    }

    this.hideContextMenu();
  }

  /**
   * 处理同步
   */
  async handleSync() {
    try {
      this.showLoading(true);
      const response = await this.sendMessage({ action: 'SYNC_DATA' });

      if (response.success) {
        await this.loadBrowserData();
        this.render();
        this.showNotification('Data synced successfully', 'success');
      } else {
        this.showError('Failed to sync data');
      }
    } catch (error) {
      console.error('Sync failed:', error);
      this.showError('Failed to sync data');
    } finally {
      this.showLoading(false);
    }
  }

  /**
   * 处理设置
   */
  handleSettings() {
    this.showNotification('Settings feature coming soon', 'info');
  }

  /**
   * 处理新窗口
   */
  async handleNewWindow() {
    try {
      const response = await this.sendMessage({
        action: 'CREATE_WINDOW',
        data: { url: 'chrome://newtab/' }
      });

      if (response.success) {
        this.showNotification('New window created', 'success');
        window.close();
      } else {
        this.showError('Failed to create new window');
      }
    } catch (error) {
      console.error('Failed to create new window:', error);
      this.showError('Failed to create new window');
    }
  }

  /**
   * 处理导出
   */
  handleExport() {
    this.showNotification('Export feature coming soon', 'info');
  }

  /**
   * 处理导入
   */
  handleImport() {
    this.showNotification('Import feature coming soon', 'info');
  }

  /**
   * 处理键盘快捷键
   */
  handleKeyboard(event) {
    // Ctrl/Cmd + F: 聚焦搜索框
    if ((event.ctrlKey || event.metaKey) && event.key === 'f') {
      event.preventDefault();
      const searchInput = document.getElementById('searchInput');
      searchInput?.focus();
    }

    // Escape: 清除搜索或关闭菜单
    if (event.key === 'Escape') {
      const contextMenu = document.getElementById('contextMenu');
      if (contextMenu.style.display === 'block') {
        this.hideContextMenu();
      } else {
        this.clearSearch();
      }
    }
  }
}

// 初始化应用
let popupController;
document.addEventListener('DOMContentLoaded', () => {
  popupController = new PopupController();
});
