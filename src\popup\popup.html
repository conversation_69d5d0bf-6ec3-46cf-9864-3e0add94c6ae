<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bookmark Tab Manager</title>
    <link rel="stylesheet" href="popup.css">
</head>
<body>
    <div id="app" class="app">
        <!-- 头部导航 -->
        <header class="header">
            <div class="header-content">
                <div class="logo">
                    <svg class="logo-icon" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-5 14H7v-2h7v2zm3-4H7v-2h10v2zm0-4H7V7h10v2z"/>
                    </svg>
                    <h1 class="logo-text">Tab Manager</h1>
                </div>
                <div class="header-actions">
                    <button id="syncBtn" class="btn btn-icon" title="Sync Data">
                        <svg viewBox="0 0 24 24" fill="currentColor">
                            <path d="M12 4V1L8 5l4 4V6c3.31 0 6 2.69 6 6 0 1.01-.25 1.97-.7 2.8l1.46 1.46C19.54 15.03 20 13.57 20 12c0-4.42-3.58-8-8-8zm0 14c-3.31 0-6-2.69-6-6 0-1.01.25-1.97.7-2.8L5.24 7.74C4.46 8.97 4 10.43 4 12c0 4.42 3.58 8 8 8v3l4-4-4-4v3z"/>
                        </svg>
                    </button>
                    <button id="settingsBtn" class="btn btn-icon" title="Settings">
                        <svg viewBox="0 0 24 24" fill="currentColor">
                            <path d="M19.14,12.94c0.04-0.3,0.06-0.61,0.06-0.94c0-0.32-0.02-0.64-0.07-0.94l2.03-1.58c0.18-0.14,0.23-0.41,0.12-0.61 l-1.92-3.32c-0.12-0.22-0.37-0.29-0.59-0.22l-2.39,0.96c-0.5-0.38-1.03-0.7-1.62-0.94L14.4,2.81c-0.04-0.24-0.24-0.41-0.48-0.41 h-3.84c-0.24,0-0.43,0.17-0.47,0.41L9.25,5.35C8.66,5.59,8.12,5.92,7.63,6.29L5.24,5.33c-0.22-0.08-0.47,0-0.59,0.22L2.74,8.87 C2.62,9.08,2.66,9.34,2.86,9.48l2.03,1.58C4.84,11.36,4.82,11.69,4.82,12s0.02,0.64,0.07,0.94l-2.03,1.58 c-0.18,0.14-0.23,0.41-0.12,0.61l1.92,3.32c0.12,0.22,0.37,0.29,0.59,0.22l2.39-0.96c0.5,0.38,1.03,0.7,1.62,0.94l0.36,2.54 c0.05,0.24,0.24,0.41,0.48,0.41h3.84c0.24,0,0.44-0.17,0.47-0.41l0.36-2.54c0.59-0.24,1.13-0.56,1.62-0.94l2.39,0.96 c0.22,0.08,0.47,0,0.59-0.22l1.92-3.32c0.12-0.22,0.07-0.47-0.12-0.61L19.14,12.94z M12,15.6c-1.98,0-3.6-1.62-3.6-3.6 s1.62-3.6,3.6-3.6s3.6,1.62,3.6,3.6S13.98,15.6,12,15.6z"/>
                        </svg>
                    </button>
                </div>
            </div>
        </header>

        <!-- 搜索栏 -->
        <div class="search-container">
            <div class="search-box">
                <svg class="search-icon" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"/>
                </svg>
                <input type="text" id="searchInput" placeholder="Search tabs, windows, or notes..." class="search-input">
                <button id="clearSearchBtn" class="btn btn-icon search-clear" title="Clear Search">
                    <svg viewBox="0 0 24 24" fill="currentColor">
                        <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
                    </svg>
                </button>
            </div>
            <div class="search-filters">
                <button id="filterAllBtn" class="filter-btn active" data-filter="all">All</button>
                <button id="filterTabsBtn" class="filter-btn" data-filter="tabs">Tabs</button>
                <button id="filterWindowsBtn" class="filter-btn" data-filter="windows">Windows</button>
                <button id="filterGroupsBtn" class="filter-btn" data-filter="groups">Groups</button>
            </div>
        </div>

        <!-- 主要内容区域 -->
        <main class="main-content">
            <!-- 加载状态 -->
            <div id="loadingState" class="loading-state">
                <div class="loading-spinner"></div>
                <p>Loading browser data...</p>
            </div>

            <!-- 空状态 -->
            <div id="emptyState" class="empty-state" style="display: none;">
                <svg class="empty-icon" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-5 14H7v-2h7v2zm3-4H7v-2h10v2zm0-4H7V7h10v2z"/>
                </svg>
                <h3>No tabs found</h3>
                <p>No tabs match your current search criteria.</p>
            </div>

            <!-- 统计信息 -->
            <div id="statsContainer" class="stats-container" style="display: none;">
                <div class="stat-item">
                    <span class="stat-number" id="totalTabs">0</span>
                    <span class="stat-label">Tabs</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number" id="totalWindows">0</span>
                    <span class="stat-label">Windows</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number" id="totalGroups">0</span>
                    <span class="stat-label">Groups</span>
                </div>
            </div>

            <!-- 窗口列表 -->
            <div id="windowsList" class="windows-list" style="display: none;">
                <!-- 窗口项目将通过JavaScript动态生成 -->
            </div>
        </main>

        <!-- 底部操作栏 -->
        <footer class="footer">
            <div class="footer-actions">
                <button id="newWindowBtn" class="btn btn-primary">
                    <svg viewBox="0 0 24 24" fill="currentColor">
                        <path d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z"/>
                    </svg>
                    New Window
                </button>
                <button id="exportBtn" class="btn btn-secondary">
                    <svg viewBox="0 0 24 24" fill="currentColor">
                        <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/>
                    </svg>
                    Export
                </button>
                <button id="importBtn" class="btn btn-secondary">
                    <svg viewBox="0 0 24 24" fill="currentColor">
                        <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/>
                    </svg>
                    Import
                </button>
            </div>
        </footer>
    </div>

    <!-- 模态框容器 -->
    <div id="modalContainer" class="modal-container" style="display: none;">
        <!-- 模态框内容将通过JavaScript动态生成 -->
    </div>

    <!-- 通知容器 -->
    <div id="notificationContainer" class="notification-container">
        <!-- 通知将通过JavaScript动态生成 -->
    </div>

    <!-- 上下文菜单 -->
    <div id="contextMenu" class="context-menu" style="display: none;">
        <!-- 上下文菜单项将通过JavaScript动态生成 -->
    </div>

    <!-- JavaScript模块 -->
    <script type="module" src="popup.js"></script>
</body>
</html>
