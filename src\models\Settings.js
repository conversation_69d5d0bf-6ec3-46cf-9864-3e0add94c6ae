/**
 * Settings数据模型
 * 管理用户设置和偏好
 */
export class Settings {
  constructor(data = {}) {
    // 基础设置
    this.theme = data.theme || 'auto'; // auto, light, dark
    this.language = data.language || 'en';
    this.autoSave = data.autoSave !== undefined ? data.autoSave : true;
    this.autoBackup = data.autoBackup !== undefined ? data.autoBackup : true;
    this.backupInterval = data.backupInterval || 24; // hours
    
    // 界面设置
    this.showFavicons = data.showFavicons !== undefined ? data.showFavicons : true;
    this.showTabCount = data.showTabCount !== undefined ? data.showTabCount : true;
    this.showWindowInfo = data.showWindowInfo !== undefined ? data.showWindowInfo : true;
    this.compactMode = data.compactMode !== undefined ? data.compactMode : false;
    this.animationsEnabled = data.animationsEnabled !== undefined ? data.animationsEnabled : true;
    
    // 功能设置
    this.enableDragDrop = data.enableDragDrop !== undefined ? data.enableDragDrop : true;
    this.enableKeyboardShortcuts = data.enableKeyboardShortcuts !== undefined ? data.enableKeyboardShortcuts : true;
    this.enableNotifications = data.enableNotifications !== undefined ? data.enableNotifications : true;
    this.enableSounds = data.enableSounds !== undefined ? data.enableSounds : false;
    
    // 搜索设置
    this.searchInUrl = data.searchInUrl !== undefined ? data.searchInUrl : true;
    this.searchInTitle = data.searchInTitle !== undefined ? data.searchInTitle : true;
    this.searchInNotes = data.searchInNotes !== undefined ? data.searchInNotes : true;
    this.searchCaseSensitive = data.searchCaseSensitive !== undefined ? data.searchCaseSensitive : false;
    this.searchRegex = data.searchRegex !== undefined ? data.searchRegex : false;
    
    // 标签页管理设置
    this.maxTabsPerWindow = data.maxTabsPerWindow || 50;
    this.warnBeforeClosing = data.warnBeforeClosing !== undefined ? data.warnBeforeClosing : true;
    this.rememberClosedTabs = data.rememberClosedTabs !== undefined ? data.rememberClosedTabs : true;
    this.maxClosedTabsHistory = data.maxClosedTabsHistory || 100;
    
    // 重复标签页设置
    this.autoDuplicateDetection = data.autoDuplicateDetection !== undefined ? data.autoDuplicateDetection : true;
    this.duplicateDetectionSensitivity = data.duplicateDetectionSensitivity || 'medium'; // low, medium, high
    this.autoCloseDuplicates = data.autoCloseDuplicates !== undefined ? data.autoCloseDuplicates : false;
    
    // 导入导出设置
    this.exportFormat = data.exportFormat || 'json'; // json, html, csv
    this.includeMetadata = data.includeMetadata !== undefined ? data.includeMetadata : true;
    this.includeClosedTabs = data.includeClosedTabs !== undefined ? data.includeClosedTabs : false;
    
    // 快捷键设置
    this.shortcuts = data.shortcuts || this.getDefaultShortcuts();
    
    // 高级设置
    this.debugMode = data.debugMode !== undefined ? data.debugMode : false;
    this.performanceMode = data.performanceMode !== undefined ? data.performanceMode : false;
    this.dataCompression = data.dataCompression !== undefined ? data.dataCompression : true;
    
    // 元数据
    this.version = data.version || '1.0.0';
    this.createdAt = data.createdAt || new Date().toISOString();
    this.updatedAt = data.updatedAt || new Date().toISOString();
  }

  /**
   * 获取默认快捷键配置
   */
  getDefaultShortcuts() {
    return {
      openManager: 'Ctrl+Shift+T',
      searchTabs: 'Ctrl+Shift+F',
      newWindow: 'Ctrl+Shift+N',
      closeWindow: 'Ctrl+Shift+W',
      duplicateTab: 'Ctrl+Shift+D',
      bookmarkTab: 'Ctrl+Shift+B',
      exportData: 'Ctrl+Shift+E',
      importData: 'Ctrl+Shift+I',
      undo: 'Ctrl+Z',
      redo: 'Ctrl+Y'
    };
  }

  /**
   * 验证设置数据
   */
  validate() {
    const errors = [];
    
    // 验证主题
    if (!['auto', 'light', 'dark'].includes(this.theme)) {
      errors.push('Invalid theme value');
    }
    
    // 验证备份间隔
    if (this.backupInterval < 1 || this.backupInterval > 168) {
      errors.push('Backup interval must be between 1 and 168 hours');
    }
    
    // 验证最大标签页数
    if (this.maxTabsPerWindow < 1 || this.maxTabsPerWindow > 1000) {
      errors.push('Max tabs per window must be between 1 and 1000');
    }
    
    // 验证重复检测敏感度
    if (!['low', 'medium', 'high'].includes(this.duplicateDetectionSensitivity)) {
      errors.push('Invalid duplicate detection sensitivity');
    }
    
    // 验证导出格式
    if (!['json', 'html', 'csv'].includes(this.exportFormat)) {
      errors.push('Invalid export format');
    }
    
    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * 更新设置
   */
  update(newSettings) {
    Object.assign(this, newSettings);
    this.updatedAt = new Date().toISOString();
  }

  /**
   * 重置为默认设置
   */
  reset() {
    const defaultSettings = new Settings();
    Object.assign(this, defaultSettings);
    this.updatedAt = new Date().toISOString();
  }

  /**
   * 获取特定类别的设置
   */
  getCategory(category) {
    const categories = {
      appearance: {
        theme: this.theme,
        showFavicons: this.showFavicons,
        showTabCount: this.showTabCount,
        showWindowInfo: this.showWindowInfo,
        compactMode: this.compactMode,
        animationsEnabled: this.animationsEnabled
      },
      behavior: {
        autoSave: this.autoSave,
        autoBackup: this.autoBackup,
        enableDragDrop: this.enableDragDrop,
        enableKeyboardShortcuts: this.enableKeyboardShortcuts,
        enableNotifications: this.enableNotifications,
        warnBeforeClosing: this.warnBeforeClosing
      },
      search: {
        searchInUrl: this.searchInUrl,
        searchInTitle: this.searchInTitle,
        searchInNotes: this.searchInNotes,
        searchCaseSensitive: this.searchCaseSensitive,
        searchRegex: this.searchRegex
      },
      advanced: {
        debugMode: this.debugMode,
        performanceMode: this.performanceMode,
        dataCompression: this.dataCompression,
        maxTabsPerWindow: this.maxTabsPerWindow,
        maxClosedTabsHistory: this.maxClosedTabsHistory
      }
    };
    
    return categories[category] || {};
  }

  /**
   * 序列化为JSON
   */
  toJSON() {
    return {
      theme: this.theme,
      language: this.language,
      autoSave: this.autoSave,
      autoBackup: this.autoBackup,
      backupInterval: this.backupInterval,
      showFavicons: this.showFavicons,
      showTabCount: this.showTabCount,
      showWindowInfo: this.showWindowInfo,
      compactMode: this.compactMode,
      animationsEnabled: this.animationsEnabled,
      enableDragDrop: this.enableDragDrop,
      enableKeyboardShortcuts: this.enableKeyboardShortcuts,
      enableNotifications: this.enableNotifications,
      enableSounds: this.enableSounds,
      searchInUrl: this.searchInUrl,
      searchInTitle: this.searchInTitle,
      searchInNotes: this.searchInNotes,
      searchCaseSensitive: this.searchCaseSensitive,
      searchRegex: this.searchRegex,
      maxTabsPerWindow: this.maxTabsPerWindow,
      warnBeforeClosing: this.warnBeforeClosing,
      rememberClosedTabs: this.rememberClosedTabs,
      maxClosedTabsHistory: this.maxClosedTabsHistory,
      autoDuplicateDetection: this.autoDuplicateDetection,
      duplicateDetectionSensitivity: this.duplicateDetectionSensitivity,
      autoCloseDuplicates: this.autoCloseDuplicates,
      exportFormat: this.exportFormat,
      includeMetadata: this.includeMetadata,
      includeClosedTabs: this.includeClosedTabs,
      shortcuts: this.shortcuts,
      debugMode: this.debugMode,
      performanceMode: this.performanceMode,
      dataCompression: this.dataCompression,
      version: this.version,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt
    };
  }
}
