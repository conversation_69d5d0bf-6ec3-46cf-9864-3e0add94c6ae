<!DOCTYPE html>
<html>
<head>
    <title>Create Icons</title>
</head>
<body>
    <canvas id="canvas" width="128" height="128"></canvas>
    <script>
        // 创建简单的图标
        function createIcon(size) {
            const canvas = document.getElementById('canvas');
            canvas.width = size;
            canvas.height = size;
            const ctx = canvas.getContext('2d');
            
            // 背景
            ctx.fillStyle = '#4285f4';
            ctx.fillRect(0, 0, size, size);
            
            // 白色图标
            ctx.fillStyle = 'white';
            const scale = size / 16;
            
            // 绘制标签页图标
            ctx.fillRect(2 * scale, 10 * scale, 1 * scale, 2 * scale);
            ctx.fillRect(4 * scale, 8 * scale, 2 * scale, 4 * scale);
            ctx.fillRect(7 * scale, 6 * scale, 2 * scale, 6 * scale);
            ctx.fillRect(10 * scale, 4 * scale, 2 * scale, 8 * scale);
            
            return canvas.toDataURL('image/png');
        }
        
        // 生成不同尺寸的图标
        const sizes = [16, 32, 48, 128];
        sizes.forEach(size => {
            const dataUrl = createIcon(size);
            console.log(`Icon ${size}x${size}:`, dataUrl);
        });
    </script>
</body>
</html>
