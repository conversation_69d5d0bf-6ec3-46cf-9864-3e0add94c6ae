/* 基础样式重置和变量 */
:root {
  --primary-color: #4285f4;
  --secondary-color: #34a853;
  --danger-color: #ea4335;
  --warning-color: #fbbc04;
  --background-color: #ffffff;
  --surface-color: #f8f9fa;
  --border-color: #e0e0e0;
  --text-primary: #202124;
  --text-secondary: #5f6368;
  --text-disabled: #9aa0a6;
  --shadow-light: 0 1px 3px rgba(0,0,0,0.12);
  --shadow-medium: 0 4px 6px rgba(0,0,0,0.16);
  --shadow-heavy: 0 10px 20px rgba(0,0,0,0.19);
  --border-radius: 8px;
  --transition: all 0.2s ease;
  --header-height: 60px;
  --footer-height: 70px;
}

/* 暗色主题 */
[data-theme="dark"] {
  --background-color: #202124;
  --surface-color: #303134;
  --border-color: #5f6368;
  --text-primary: #e8eaed;
  --text-secondary: #9aa0a6;
  --text-disabled: #5f6368;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  font-size: 14px;
  line-height: 1.5;
  color: var(--text-primary);
  background-color: var(--background-color);
  overflow-x: hidden;
}

/* 应用容器 */
.app {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

/* 头部样式 */
.header {
  background-color: var(--surface-color);
  border-bottom: 1px solid var(--border-color);
  height: var(--header-height);
  flex-shrink: 0;
  position: sticky;
  top: 0;
  z-index: 100;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 100%;
  padding: 0 24px;
  max-width: 1400px;
  margin: 0 auto;
}

.logo {
  display: flex;
  align-items: center;
  gap: 12px;
}

.logo-icon {
  width: 32px;
  height: 32px;
  color: var(--primary-color);
}

.logo-text {
  font-size: 20px;
  font-weight: 600;
  color: var(--text-primary);
}

.header-actions {
  display: flex;
  gap: 8px;
}

/* 按钮样式 */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 10px 16px;
  border: none;
  border-radius: var(--border-radius);
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition);
  text-decoration: none;
  background: transparent;
  color: var(--text-primary);
  white-space: nowrap;
}

.btn:hover {
  background-color: var(--border-color);
}

.btn:active {
  transform: scale(0.98);
}

.btn-icon {
  padding: 10px;
  border-radius: 50%;
}

.btn-icon svg {
  width: 20px;
  height: 20px;
}

.btn-primary {
  background-color: var(--primary-color);
  color: white;
}

.btn-primary:hover {
  background-color: #3367d6;
}

.btn-secondary {
  background-color: var(--surface-color);
  border: 1px solid var(--border-color);
}

.btn-secondary:hover {
  background-color: var(--border-color);
}

/* 搜索区域 */
.search-section {
  background-color: var(--background-color);
  border-bottom: 1px solid var(--border-color);
  padding: 20px 24px;
  flex-shrink: 0;
}

.search-container {
  max-width: 1400px;
  margin: 0 auto;
}

.search-box {
  position: relative;
  display: flex;
  align-items: center;
  background-color: var(--surface-color);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: 12px 16px;
  margin-bottom: 16px;
  max-width: 600px;
}

.search-icon {
  width: 20px;
  height: 20px;
  color: var(--text-secondary);
  margin-right: 12px;
  flex-shrink: 0;
}

.search-input {
  flex: 1;
  border: none;
  background: transparent;
  font-size: 16px;
  color: var(--text-primary);
  outline: none;
}

.search-input::placeholder {
  color: var(--text-secondary);
}

.search-clear {
  opacity: 0;
  transition: var(--transition);
  margin-left: 8px;
}

.search-clear.visible {
  opacity: 1;
}

.search-filters {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.filter-btn {
  padding: 8px 16px;
  border: none;
  background: transparent;
  color: var(--text-secondary);
  border-radius: 20px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition);
}

.filter-btn:hover {
  background-color: var(--border-color);
}

.filter-btn.active {
  background-color: var(--primary-color);
  color: white;
}

/* 统计区域 */
.stats-section {
  background-color: var(--surface-color);
  border-bottom: 1px solid var(--border-color);
  padding: 20px 24px;
  flex-shrink: 0;
}

.stats-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  max-width: 1400px;
  margin: 0 auto;
}

.stat-card {
  background-color: var(--background-color);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: 20px;
  text-align: center;
  transition: var(--transition);
}

.stat-card:hover {
  box-shadow: var(--shadow-light);
}

.stat-number {
  display: block;
  font-size: 32px;
  font-weight: 700;
  color: var(--primary-color);
  line-height: 1;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 14px;
  color: var(--text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-weight: 500;
}

/* 主要内容区域 */
.main-content {
  flex: 1;
  padding: 24px;
  max-width: 1400px;
  margin: 0 auto;
  width: 100%;
  overflow-y: auto;
}

/* 加载状态 */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid var(--border-color);
  border-top: 4px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
}

.empty-icon {
  width: 64px;
  height: 64px;
  color: var(--text-disabled);
  margin-bottom: 20px;
}

.empty-state h3 {
  font-size: 20px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 12px;
}

.empty-state p {
  color: var(--text-secondary);
  font-size: 16px;
}

/* 窗口列表 */
.windows-list {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.window-item {
  background-color: var(--surface-color);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  overflow: hidden;
  transition: var(--transition);
}

.window-item:hover {
  box-shadow: var(--shadow-light);
}

.window-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  background-color: var(--background-color);
  border-bottom: 1px solid var(--border-color);
  cursor: pointer;
  transition: var(--transition);
}

.window-header:hover {
  background-color: var(--surface-color);
}

.window-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.window-icon {
  width: 20px;
  height: 20px;
  color: var(--text-secondary);
  flex-shrink: 0;
}

.window-title {
  font-weight: 600;
  color: var(--text-primary);
  font-size: 16px;
}

.window-meta {
  font-size: 14px;
  color: var(--text-secondary);
  margin-top: 4px;
}

.window-actions {
  display: flex;
  gap: 4px;
}

/* 横向标签页布局 - 关键修复 */
.window-tabs {
  padding: 16px 20px;
  background-color: var(--surface-color);
}

.tabs-container {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  align-items: flex-start;
}

.tab-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background-color: var(--background-color);
  border: 1px solid var(--border-color);
  border-radius: 6px;
  cursor: pointer;
  transition: var(--transition);
  min-width: 200px;
  max-width: 300px;
  position: relative;
}

.tab-item:hover {
  background-color: var(--border-color);
  box-shadow: var(--shadow-light);
}

.tab-item.active {
  border-color: var(--primary-color);
  background-color: rgba(66, 133, 244, 0.1);
}

.tab-favicon {
  width: 16px;
  height: 16px;
  border-radius: 2px;
  flex-shrink: 0;
}

.tab-info {
  flex: 1;
  min-width: 0;
}

.tab-title {
  font-size: 13px;
  font-weight: 500;
  color: var(--text-primary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.3;
}

.tab-url {
  font-size: 11px;
  color: var(--text-secondary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-top: 2px;
}

.tab-actions {
  display: flex;
  gap: 2px;
  opacity: 0;
  transition: var(--transition);
}

.tab-item:hover .tab-actions {
  opacity: 1;
}

.tab-close {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-secondary);
  transition: var(--transition);
}

.tab-close:hover {
  background-color: var(--danger-color);
  color: white;
}

.tab-close svg {
  width: 12px;
  height: 12px;
}

/* 底部操作栏 */
.footer {
  background-color: var(--surface-color);
  border-top: 1px solid var(--border-color);
  height: var(--footer-height);
  flex-shrink: 0;
  padding: 16px 24px;
}

.footer-actions {
  display: flex;
  gap: 12px;
  max-width: 1400px;
  margin: 0 auto;
  justify-content: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header-content {
    padding: 0 16px;
  }
  
  .search-section {
    padding: 16px;
  }
  
  .stats-section {
    padding: 16px;
  }
  
  .main-content {
    padding: 16px;
  }
  
  .footer {
    padding: 12px 16px;
  }
  
  .footer-actions {
    flex-wrap: wrap;
    justify-content: center;
  }
  
  .tab-item {
    min-width: 150px;
    max-width: 250px;
  }
  
  .stats-container {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 480px) {
  .logo-text {
    display: none;
  }

  .stats-container {
    grid-template-columns: 1fr;
  }

  .tab-item {
    min-width: 120px;
    max-width: 200px;
  }
}

/* 模态框 */
.modal-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal {
  background-color: var(--background-color);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-heavy);
  max-width: 90%;
  max-height: 90%;
  overflow: hidden;
}

.modal-header {
  padding: 20px 24px;
  border-bottom: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.modal-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
}

.modal-body {
  padding: 24px;
  overflow-y: auto;
}

.modal-footer {
  padding: 20px 24px;
  border-top: 1px solid var(--border-color);
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}

/* 通知系统 */
.notification-container {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 1100;
  display: flex;
  flex-direction: column;
  gap: 12px;
  max-width: 400px;
}

.notification {
  background-color: var(--background-color);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-medium);
  animation: slideInRight 0.3s ease;
  overflow: hidden;
}

.notification-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  gap: 12px;
}

.notification-close {
  background: none;
  border: none;
  font-size: 18px;
  color: var(--text-secondary);
  cursor: pointer;
  padding: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: var(--transition);
}

.notification-close:hover {
  background-color: var(--border-color);
}

.notification.success {
  border-left: 4px solid var(--secondary-color);
}

.notification.error {
  border-left: 4px solid var(--danger-color);
}

.notification.warning {
  border-left: 4px solid var(--warning-color);
}

.notification.info {
  border-left: 4px solid var(--primary-color);
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* 上下文菜单 */
.context-menu {
  position: fixed;
  background-color: var(--background-color);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-medium);
  padding: 8px 0;
  min-width: 180px;
  z-index: 1200;
  animation: fadeIn 0.15s ease;
}

.context-menu-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  cursor: pointer;
  font-size: 14px;
  color: var(--text-primary);
  transition: var(--transition);
}

.context-menu-item:hover {
  background-color: var(--border-color);
}

.context-menu-item svg {
  width: 16px;
  height: 16px;
  color: var(--text-secondary);
  flex-shrink: 0;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* 全屏模式 */
.app:-webkit-full-screen {
  background-color: var(--background-color);
}

.app:-moz-full-screen {
  background-color: var(--background-color);
}

.app:fullscreen {
  background-color: var(--background-color);
}

/* 移动端优化 */
.mobile .header-content {
  padding: 0 12px;
}

.mobile .search-section {
  padding: 12px;
}

.mobile .stats-section {
  padding: 12px;
}

.mobile .main-content {
  padding: 12px;
}

.mobile .footer {
  padding: 12px;
}

.mobile .footer-actions {
  flex-direction: column;
  gap: 8px;
}

.mobile .tab-item {
  min-width: 100%;
  max-width: 100%;
}

.mobile .tabs-container {
  flex-direction: column;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--surface-color);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: var(--border-color);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--text-secondary);
}

/* 选择状态 */
::selection {
  background-color: rgba(66, 133, 244, 0.2);
}

/* 焦点状态 */
.btn:focus,
.search-input:focus,
.filter-btn:focus {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

/* 动画优化 */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}
