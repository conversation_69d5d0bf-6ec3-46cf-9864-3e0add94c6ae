/**
 * TabGroup数据模型
 * 管理标签组的所有属性和操作
 */
export class TabGroup {
  constructor(data = {}) {
    this.id = data.id || null;
    this.windowId = data.windowId || null;
    this.title = data.title || '';
    this.color = data.color || 'grey';
    this.collapsed = data.collapsed || false;
    
    // 扩展属性
    this.description = data.description || '';
    this.tags = data.tags || [];
    this.category = data.category || '';
    this.createdAt = data.createdAt || new Date().toISOString();
    this.lastAccessed = data.lastAccessed || new Date().toISOString();
    this.tabIds = data.tabIds || [];
    this.customData = data.customData || {};
    this.sortOrder = data.sortOrder || 0;
  }

  /**
   * 从Chrome TabGroup对象创建TabGroup实例
   */
  static fromChromeTabGroup(chromeTabGroup) {
    return new TabGroup({
      id: chromeTabGroup.id,
      windowId: chromeTabGroup.windowId,
      title: chromeTabGroup.title,
      color: chromeTabGroup.color,
      collapsed: chromeTabGroup.collapsed
    });
  }

  /**
   * 转换为Chrome TabGroup格式
   */
  toChromeTabGroup() {
    return {
      id: this.id,
      windowId: this.windowId,
      title: this.title,
      color: this.color,
      collapsed: this.collapsed
    };
  }

  /**
   * 添加标签页ID
   */
  addTabId(tabId) {
    if (!this.tabIds.includes(tabId)) {
      this.tabIds.push(tabId);
    }
  }

  /**
   * 移除标签页ID
   */
  removeTabId(tabId) {
    this.tabIds = this.tabIds.filter(id => id !== tabId);
  }

  /**
   * 获取标签页数量
   */
  getTabCount() {
    return this.tabIds.length;
  }

  /**
   * 检查是否包含指定标签页
   */
  hasTab(tabId) {
    return this.tabIds.includes(tabId);
  }

  /**
   * 验证数据有效性
   */
  validate() {
    const errors = [];
    
    if (!this.title.trim()) {
      errors.push('Title is required');
    }
    
    const validColors = ['grey', 'blue', 'red', 'yellow', 'green', 'pink', 'purple', 'cyan'];
    if (!validColors.includes(this.color)) {
      errors.push('Invalid color');
    }
    
    if (this.windowId !== null && typeof this.windowId !== 'number') {
      errors.push('WindowId must be a number or null');
    }
    
    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * 序列化为JSON
   */
  toJSON() {
    return {
      id: this.id,
      windowId: this.windowId,
      title: this.title,
      color: this.color,
      collapsed: this.collapsed,
      description: this.description,
      tags: this.tags,
      category: this.category,
      createdAt: this.createdAt,
      lastAccessed: this.lastAccessed,
      tabIds: this.tabIds,
      customData: this.customData,
      sortOrder: this.sortOrder
    };
  }

  /**
   * 克隆标签组
   */
  clone() {
    return new TabGroup(this.toJSON());
  }

  /**
   * 更新访问信息
   */
  updateAccess() {
    this.lastAccessed = new Date().toISOString();
  }

  /**
   * 检查是否匹配搜索条件
   */
  matches(searchTerm) {
    const term = searchTerm.toLowerCase();
    return this.title.toLowerCase().includes(term) ||
           this.description.toLowerCase().includes(term) ||
           this.tags.some(tag => tag.toLowerCase().includes(term));
  }

  /**
   * 获取颜色的十六进制值
   */
  getColorHex() {
    const colorMap = {
      grey: '#5f6368',
      blue: '#1a73e8',
      red: '#d93025',
      yellow: '#fbbc04',
      green: '#34a853',
      pink: '#f439a0',
      purple: '#9c27b0',
      cyan: '#00bcd4'
    };
    return colorMap[this.color] || colorMap.grey;
  }

  /**
   * 设置颜色
   */
  setColor(color) {
    const validColors = ['grey', 'blue', 'red', 'yellow', 'green', 'pink', 'purple', 'cyan'];
    if (validColors.includes(color)) {
      this.color = color;
      return true;
    }
    return false;
  }

  /**
   * 切换折叠状态
   */
  toggleCollapsed() {
    this.collapsed = !this.collapsed;
    this.updateAccess();
  }

  /**
   * 获取组摘要信息
   */
  getSummary() {
    return {
      id: this.id,
      title: this.title,
      color: this.color,
      collapsed: this.collapsed,
      tabCount: this.getTabCount(),
      windowId: this.windowId
    };
  }
}
