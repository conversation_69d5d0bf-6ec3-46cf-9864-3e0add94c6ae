/**
 * Service Worker - Chrome扩展后台脚本
 * 处理扩展生命周期事件和消息通信
 */

// 简化的本地存储管理器
class SimpleStorageManager {
  constructor() {
    this.storage = chrome.storage.local;
  }

  async set(key, value) {
    try {
      await this.storage.set({ [key]: value });
      return true;
    } catch (error) {
      console.error('Storage set error:', error);
      return false;
    }
  }

  async get(key, defaultValue = null) {
    try {
      const result = await this.storage.get(key);
      return result.hasOwnProperty(key) ? result[key] : defaultValue;
    } catch (error) {
      console.error('Storage get error:', error);
      return defaultValue;
    }
  }

  async remove(key) {
    try {
      await this.storage.remove(key);
      return true;
    } catch (error) {
      console.error('Storage remove error:', error);
      return false;
    }
  }
}

// 简化的Chrome API包装器
class SimpleChromeAPI {
  async queryTabs(queryInfo = {}) {
    try {
      return await chrome.tabs.query(queryInfo);
    } catch (error) {
      console.error('Query tabs error:', error);
      throw error;
    }
  }

  async getAllWindows() {
    try {
      return await chrome.windows.getAll({ populate: true, windowTypes: ['normal'] });
    } catch (error) {
      console.error('Get all windows error:', error);
      throw error;
    }
  }

  async createTab(createProperties) {
    try {
      return await chrome.tabs.create(createProperties);
    } catch (error) {
      console.error('Create tab error:', error);
      throw error;
    }
  }

  async removeTabs(tabIds) {
    try {
      const ids = Array.isArray(tabIds) ? tabIds : [tabIds];
      return await chrome.tabs.remove(ids);
    } catch (error) {
      console.error('Remove tabs error:', error);
      throw error;
    }
  }

  async activateTab(tabId) {
    try {
      return await chrome.tabs.update(tabId, { active: true });
    } catch (error) {
      console.error('Activate tab error:', error);
      throw error;
    }
  }

  async createWindow(createData) {
    try {
      return await chrome.windows.create(createData);
    } catch (error) {
      console.error('Create window error:', error);
      throw error;
    }
  }

  async updateWindow(windowId, updateInfo) {
    try {
      return await chrome.windows.update(windowId, updateInfo);
    } catch (error) {
      console.error('Update window error:', error);
      throw error;
    }
  }

  async removeWindow(windowId) {
    try {
      return await chrome.windows.remove(windowId);
    } catch (error) {
      console.error('Remove window error:', error);
      throw error;
    }
  }
}

class ServiceWorker {
  constructor() {
    this.chromeAPI = new SimpleChromeAPI();
    this.storage = new SimpleStorageManager();
    this.isInitialized = false;
    this.syncInProgress = false;
    this.lastSyncTime = null;

    this.init();
  }

  /**
   * 初始化Service Worker
   */
  async init() {
    try {
      console.log('Service Worker initializing...');
      
      // 设置事件监听器
      this.setupEventListeners();
      
      // 初始化数据同步
      await this.initializeDataSync();
      
      this.isInitialized = true;
      console.log('Service Worker initialized successfully');
      
      // 执行初始数据同步
      await this.syncBrowserData();
      
    } catch (error) {
      console.error('Service Worker initialization failed:', error);
    }
  }

  /**
   * 设置事件监听器
   */
  setupEventListeners() {
    // 扩展安装/启动事件
    chrome.runtime.onInstalled.addListener((details) => {
      this.handleInstalled(details);
    });

    chrome.runtime.onStartup.addListener(() => {
      this.handleStartup();
    });

    // 点击扩展图标事件
    chrome.action.onClicked.addListener((tab) => {
      this.handleActionClicked(tab);
    });

    // 消息通信
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
      this.handleMessage(message, sender, sendResponse);
      return true; // 保持消息通道开放
    });

    // 标签页事件
    chrome.tabs.onCreated.addListener((tab) => {
      this.handleTabCreated(tab);
    });

    chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
      this.handleTabUpdated(tabId, changeInfo, tab);
    });

    chrome.tabs.onRemoved.addListener((tabId, removeInfo) => {
      this.handleTabRemoved(tabId, removeInfo);
    });

    chrome.tabs.onMoved.addListener((tabId, moveInfo) => {
      this.handleTabMoved(tabId, moveInfo);
    });

    chrome.tabs.onActivated.addListener((activeInfo) => {
      this.handleTabActivated(activeInfo);
    });

    // 窗口事件
    chrome.windows.onCreated.addListener((window) => {
      this.handleWindowCreated(window);
    });

    chrome.windows.onRemoved.addListener((windowId) => {
      this.handleWindowRemoved(windowId);
    });

    chrome.windows.onFocusChanged.addListener((windowId) => {
      this.handleWindowFocusChanged(windowId);
    });

    // 标签组事件（如果支持）
    if (chrome.tabGroups) {
      chrome.tabGroups.onCreated?.addListener((group) => {
        this.handleTabGroupCreated(group);
      });

      chrome.tabGroups.onUpdated?.addListener((group) => {
        this.handleTabGroupUpdated(group);
      });

      chrome.tabGroups.onRemoved?.addListener((group) => {
        this.handleTabGroupRemoved(group);
      });
    }
  }

  /**
   * 初始化数据同步
   */
  async initializeDataSync() {
    try {
      // 检查是否是首次安装
      const isFirstInstall = await this.storage.get('isFirstInstall', true);
      
      if (isFirstInstall) {
        console.log('First install detected, initializing default data...');
        await this.initializeDefaultData();
        await this.storage.set('isFirstInstall', false);
      }

      // 设置定期同步
      this.setupPeriodicSync();
      
    } catch (error) {
      console.error('Data sync initialization failed:', error);
    }
  }

  /**
   * 初始化默认数据
   */
  async initializeDefaultData() {
    try {
      // 创建默认设置
      const defaultSettings = {
        theme: 'auto',
        autoSave: true,
        showFavicons: true,
        enableNotifications: true
      };
      
      await this.storage.set('settings', defaultSettings);
      await this.storage.set('tabs', {});
      await this.storage.set('windows', {});
      await this.storage.set('tabGroups', {});
      await this.storage.set('closedTabs', []);
      
      console.log('Default data initialized');
    } catch (error) {
      console.error('Failed to initialize default data:', error);
    }
  }

  /**
   * 设置定期同步
   */
  setupPeriodicSync() {
    // 每5分钟同步一次数据
    setInterval(() => {
      if (!this.syncInProgress) {
        this.syncBrowserData();
      }
    }, 5 * 60 * 1000);
  }

  /**
   * 同步浏览器数据
   */
  async syncBrowserData() {
    if (this.syncInProgress) {
      return;
    }

    try {
      this.syncInProgress = true;
      console.log('Starting browser data sync...');

      // 获取所有窗口和标签页
      const windows = await this.chromeAPI.getAllWindows();

      // 转换并存储数据
      const windowsData = {};
      const tabsData = {};

      for (const chromeWindow of windows) {
        // 简化的窗口数据
        windowsData[chromeWindow.id] = {
          id: chromeWindow.id,
          focused: chromeWindow.focused,
          incognito: chromeWindow.incognito,
          type: chromeWindow.type,
          state: chromeWindow.state,
          name: `Window ${chromeWindow.id}`,
          createdAt: new Date().toISOString()
        };

        // 处理窗口中的标签页
        if (chromeWindow.tabs) {
          for (const chromeTab of chromeWindow.tabs) {
            // 简化的标签页数据
            tabsData[chromeTab.id] = {
              id: chromeTab.id,
              url: chromeTab.url,
              title: chromeTab.title,
              favicon: chromeTab.favIconUrl,
              windowId: chromeTab.windowId,
              index: chromeTab.index,
              active: chromeTab.active,
              pinned: chromeTab.pinned,
              audible: chromeTab.audible,
              incognito: chromeTab.incognito,
              createdAt: new Date().toISOString(),
              lastAccessed: new Date().toISOString()
            };
          }
        }
      }

      // 保存到存储
      await this.storage.set('windows', windowsData);
      await this.storage.set('tabs', tabsData);
      await this.storage.set('tabGroups', {});
      await this.storage.set('lastSyncTime', new Date().toISOString());

      this.lastSyncTime = new Date();
      console.log('Browser data sync completed');

    } catch (error) {
      console.error('Browser data sync failed:', error);
    } finally {
      this.syncInProgress = false;
    }
  }

  /**
   * 处理消息
   */
  async handleMessage(message, sender, sendResponse) {
    try {
      const { action, data } = message;

      switch (action) {
        case 'GET_BROWSER_DATA':
          const browserData = await this.getBrowserData();
          sendResponse({ success: true, data: browserData });
          break;

        case 'SYNC_DATA':
          await this.syncBrowserData();
          sendResponse({ success: true });
          break;

        case 'CREATE_TAB':
          const newTab = await this.chromeAPI.createTab(data);
          sendResponse({ success: true, data: newTab });
          break;

        case 'CLOSE_TABS':
          await this.chromeAPI.removeTabs(data.tabIds);
          sendResponse({ success: true });
          break;

        case 'MOVE_TABS':
          await this.chromeAPI.moveTabs(data.tabIds, data.moveProperties);
          sendResponse({ success: true });
          break;

        case 'CREATE_WINDOW':
          const newWindow = await this.chromeAPI.createWindow(data);
          sendResponse({ success: true, data: newWindow });
          break;

        case 'GET_SETTINGS':
          const settings = await this.storage.get('settings', {});
          sendResponse({ success: true, data: settings });
          break;

        case 'SAVE_SETTINGS':
          await this.storage.set('settings', data);
          sendResponse({ success: true });
          break;

        case 'ACTIVATE_TAB':
          await this.chromeAPI.activateTab(data.tabId);
          sendResponse({ success: true });
          break;

        case 'FOCUS_WINDOW':
          await this.chromeAPI.updateWindow(data.windowId, { focused: true });
          sendResponse({ success: true });
          break;

        case 'CLOSE_WINDOW':
          await this.chromeAPI.removeWindow(data.windowId);
          sendResponse({ success: true });
          break;

        default:
          sendResponse({ success: false, error: 'Unknown action' });
      }
    } catch (error) {
      console.error('Message handling error:', error);
      sendResponse({ success: false, error: error.message });
    }
  }

  /**
   * 获取浏览器数据
   */
  async getBrowserData() {
    try {
      const [tabs, windows, tabGroups, settings] = await Promise.all([
        this.storage.get('tabs', {}),
        this.storage.get('windows', {}),
        this.storage.get('tabGroups', {}),
        this.storage.get('settings', {})
      ]);

      return {
        tabs,
        windows,
        tabGroups,
        settings,
        lastSyncTime: this.lastSyncTime
      };
    } catch (error) {
      console.error('Failed to get browser data:', error);
      return { tabs: {}, windows: {}, tabGroups: {}, settings: {} };
    }
  }

  // 事件处理方法
  async handleInstalled(details) {
    console.log('Extension installed:', details);
    if (details.reason === 'install') {
      await this.syncBrowserData();
    }
  }

  async handleStartup() {
    console.log('Extension startup');
    await this.syncBrowserData();
  }

  async handleActionClicked(tab) {
    console.log('Extension icon clicked');
    try {
      // 创建新标签页打开完整的管理界面
      await this.chromeAPI.createTab({
        url: chrome.runtime.getURL('index.html'),
        active: true
      });
    } catch (error) {
      console.error('Failed to open tab manager:', error);
    }
  }

  async handleTabCreated(tab) {
    console.log('Tab created:', tab.id);
    const tabs = await this.storage.get('tabs', {});
    tabs[tab.id] = {
      id: tab.id,
      url: tab.url,
      title: tab.title,
      favicon: tab.favIconUrl,
      windowId: tab.windowId,
      index: tab.index,
      active: tab.active,
      pinned: tab.pinned,
      audible: tab.audible,
      incognito: tab.incognito,
      createdAt: new Date().toISOString(),
      lastAccessed: new Date().toISOString()
    };
    await this.storage.set('tabs', tabs);
  }

  async handleTabUpdated(tabId, changeInfo, tab) {
    if (changeInfo.status === 'complete') {
      console.log('Tab updated:', tabId);
      const tabs = await this.storage.get('tabs', {});
      tabs[tabId] = {
        id: tab.id,
        url: tab.url,
        title: tab.title,
        favicon: tab.favIconUrl,
        windowId: tab.windowId,
        index: tab.index,
        active: tab.active,
        pinned: tab.pinned,
        audible: tab.audible,
        incognito: tab.incognito,
        createdAt: tabs[tabId]?.createdAt || new Date().toISOString(),
        lastAccessed: new Date().toISOString()
      };
      await this.storage.set('tabs', tabs);
    }
  }

  async handleTabRemoved(tabId, removeInfo) {
    console.log('Tab removed:', tabId);
    const tabs = await this.storage.get('tabs', {});
    
    // 保存到已关闭标签页历史
    if (tabs[tabId]) {
      const closedTabs = await this.storage.get('closedTabs', []);
      closedTabs.unshift({
        ...tabs[tabId],
        closedAt: new Date().toISOString(),
        windowId: removeInfo.windowId
      });
      
      // 限制历史记录数量
      if (closedTabs.length > 100) {
        closedTabs.splice(100);
      }
      
      await this.storage.set('closedTabs', closedTabs);
    }
    
    delete tabs[tabId];
    await this.storage.set('tabs', tabs);
  }

  async handleTabMoved(tabId, moveInfo) {
    console.log('Tab moved:', tabId);
    // 更新标签页索引信息
    await this.syncBrowserData();
  }

  async handleTabActivated(activeInfo) {
    console.log('Tab activated:', activeInfo.tabId);
    const tabs = await this.storage.get('tabs', {});
    if (tabs[activeInfo.tabId]) {
      tabs[activeInfo.tabId].lastAccessed = new Date().toISOString();
      tabs[activeInfo.tabId].accessCount = (tabs[activeInfo.tabId].accessCount || 0) + 1;
      await this.storage.set('tabs', tabs);
    }
  }

  async handleWindowCreated(window) {
    console.log('Window created:', window.id);
    const windows = await this.storage.get('windows', {});
    windows[window.id] = {
      id: window.id,
      focused: window.focused,
      incognito: window.incognito,
      type: window.type,
      state: window.state,
      name: `Window ${window.id}`,
      createdAt: new Date().toISOString(),
      lastAccessed: new Date().toISOString()
    };
    await this.storage.set('windows', windows);
  }

  async handleWindowRemoved(windowId) {
    console.log('Window removed:', windowId);
    const windows = await this.storage.get('windows', {});
    delete windows[windowId];
    await this.storage.set('windows', windows);
  }

  async handleWindowFocusChanged(windowId) {
    if (windowId !== chrome.windows.WINDOW_ID_NONE) {
      console.log('Window focus changed:', windowId);
      const windows = await this.storage.get('windows', {});
      if (windows[windowId]) {
        windows[windowId].lastAccessed = new Date().toISOString();
        await this.storage.set('windows', windows);
      }
    }
  }

  async handleTabGroupCreated(group) {
    console.log('Tab group created:', group.id);
    const tabGroups = await this.storage.get('tabGroups', {});
    tabGroups[group.id] = {
      id: group.id,
      windowId: group.windowId,
      title: group.title,
      color: group.color,
      collapsed: group.collapsed,
      createdAt: new Date().toISOString()
    };
    await this.storage.set('tabGroups', tabGroups);
  }

  async handleTabGroupUpdated(group) {
    console.log('Tab group updated:', group.id);
    const tabGroups = await this.storage.get('tabGroups', {});
    if (tabGroups[group.id]) {
      tabGroups[group.id] = {
        ...tabGroups[group.id],
        title: group.title,
        color: group.color,
        collapsed: group.collapsed,
        lastAccessed: new Date().toISOString()
      };
      await this.storage.set('tabGroups', tabGroups);
    }
  }

  async handleTabGroupRemoved(group) {
    console.log('Tab group removed:', group.id);
    const tabGroups = await this.storage.get('tabGroups', {});
    delete tabGroups[group.id];
    await this.storage.set('tabGroups', tabGroups);
  }
}

// 创建Service Worker实例
const serviceWorker = new ServiceWorker();
