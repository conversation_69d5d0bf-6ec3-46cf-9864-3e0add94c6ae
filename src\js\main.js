/**
 * 主应用控制器
 * 管理完整的标签页管理界面
 */

class TabManagerApp {
  constructor() {
    this.browserData = null;
    this.filteredData = null;
    this.currentFilter = 'all';
    this.searchTerm = '';
    this.isLoading = true;
    this.isFullscreen = false;
    this.selectedTabs = new Set();

    this.init();
  }

  /**
   * 初始化应用
   */
  async init() {
    try {
      console.log('Initializing Tab Manager App...');

      // 绑定事件监听器
      this.bindEventListeners();

      // 初始化空数据
      this.browserData = {
        tabs: {},
        windows: {},
        tabGroups: {},
        settings: {}
      };
      this.filteredData = this.browserData;

      // 显示初始状态
      this.showLoading(false);
      this.render();

      console.log('Tab Manager App initialized successfully');
    } catch (error) {
      console.error('App initialization failed:', error);
      this.showError('标签页管理器初始化失败');
    }
  }

  /**
   * 绑定事件监听器
   */
  bindEventListeners() {
    // 搜索功能
    const searchInput = document.getElementById('searchInput');
    const clearSearchBtn = document.getElementById('clearSearchBtn');
    
    searchInput?.addEventListener('input', (e) => {
      this.handleSearch(e.target.value);
    });
    
    clearSearchBtn?.addEventListener('click', () => {
      this.clearSearch();
    });

    // 筛选按钮
    const filterButtons = document.querySelectorAll('.filter-btn');
    filterButtons.forEach(btn => {
      btn.addEventListener('click', (e) => {
        this.handleFilter(e.target.dataset.filter);
      });
    });

    // 头部操作按钮
    const syncBtn = document.getElementById('syncBtn');
    const settingsBtn = document.getElementById('settingsBtn');
    const fullscreenBtn = document.getElementById('fullscreenBtn');
    
    syncBtn?.addEventListener('click', () => {
      this.handleSync();
    });
    
    settingsBtn?.addEventListener('click', () => {
      this.handleSettings();
    });
    
    fullscreenBtn?.addEventListener('click', () => {
      this.toggleFullscreen();
    });

    // 底部操作按钮
    const newWindowBtn = document.getElementById('newWindowBtn');
    const exportBtn = document.getElementById('exportBtn');
    const importBtn = document.getElementById('importBtn');
    const duplicatesBtn = document.getElementById('duplicatesBtn');
    
    newWindowBtn?.addEventListener('click', () => {
      this.handleNewWindow();
    });
    
    exportBtn?.addEventListener('click', () => {
      this.handleExport();
    });
    
    importBtn?.addEventListener('click', () => {
      this.handleImport();
    });
    
    duplicatesBtn?.addEventListener('click', () => {
      this.handleFindDuplicates();
    });

    // 批量操作按钮
    const batchOpenBtn = document.getElementById('batchOpenBtn');
    const batchCloseBtn = document.getElementById('batchCloseBtn');
    const batchGroupBtn = document.getElementById('batchGroupBtn');
    const batchBookmarkBtn = document.getElementById('batchBookmarkBtn');
    const clearSelectionBtn = document.getElementById('clearSelectionBtn');

    batchOpenBtn?.addEventListener('click', () => {
      this.handleBatchOpen();
    });

    batchCloseBtn?.addEventListener('click', () => {
      this.handleBatchClose();
    });

    batchGroupBtn?.addEventListener('click', () => {
      this.handleBatchGroup();
    });

    batchBookmarkBtn?.addEventListener('click', () => {
      this.handleBatchBookmark();
    });

    clearSelectionBtn?.addEventListener('click', () => {
      this.clearSelection();
    });

    // 标签页列表全选按钮
    const selectAllTabsBtn = document.getElementById('selectAllTabsBtn');
    const deselectAllTabsBtn = document.getElementById('deselectAllTabsBtn');
    const toggleSelectAllTabsBtn = document.getElementById('toggleSelectAllTabsBtn');

    selectAllTabsBtn?.addEventListener('click', () => {
      this.selectAllTabs();
    });

    deselectAllTabsBtn?.addEventListener('click', () => {
      this.deselectAllTabs();
    });

    toggleSelectAllTabsBtn?.addEventListener('click', () => {
      this.toggleSelectAllTabs();
    });

    // 键盘快捷键
    document.addEventListener('keydown', (e) => {
      this.handleKeyboard(e);
    });

    // 点击外部关闭上下文菜单
    document.addEventListener('click', (e) => {
      this.hideContextMenu();
    });

    // 窗口大小变化
    window.addEventListener('resize', () => {
      this.handleResize();
    });
  }

  /**
   * 加载浏览器数据
   */
  async loadBrowserData() {
    try {
      this.showLoading(true);
      
      // 从Service Worker获取数据
      const response = await this.sendMessage({
        action: 'GET_BROWSER_DATA'
      });
      
      if (response.success) {
        this.browserData = response.data;
        this.filteredData = this.browserData;
        console.log('Browser data loaded:', this.browserData);
      } else {
        throw new Error(response.error || 'Failed to load browser data');
      }
    } catch (error) {
      console.error('Failed to load browser data:', error);
      this.showError('加载浏览器数据失败');
    } finally {
      this.showLoading(false);
    }
  }

  /**
   * 加载演示数据
   */
  loadDemoData() {
    this.browserData = {
      tabs: {
        1: { id: 1, title: '百度一下，你就知道', url: 'https://www.baidu.com', windowId: 1, active: true, favicon: 'https://www.baidu.com/favicon.ico' },
        2: { id: 2, title: 'GitHub - 代码托管平台', url: 'https://github.com', windowId: 1, active: false, favicon: 'https://github.com/favicon.ico' },
        3: { id: 3, title: 'Stack Overflow - 程序员问答社区', url: 'https://stackoverflow.com', windowId: 2, active: true, favicon: 'https://stackoverflow.com/favicon.ico' },
        4: { id: 4, title: 'MDN Web 文档', url: 'https://developer.mozilla.org', windowId: 2, active: false, favicon: 'https://developer.mozilla.org/favicon.ico' },
        5: { id: 5, title: '知乎 - 有问题，就会有答案', url: 'https://www.zhihu.com', windowId: 1, active: false, favicon: 'https://www.zhihu.com/favicon.ico' },
        6: { id: 6, title: 'B站 - 哔哩哔哩', url: 'https://www.bilibili.com', windowId: 2, active: false, favicon: 'https://www.bilibili.com/favicon.ico' }
      },
      windows: {
        1: { id: 1, name: '主窗口', focused: true },
        2: { id: 2, name: '开发窗口', focused: false }
      },
      tabGroups: {},
      settings: {}
    };
    this.filteredData = this.browserData;
    this.showLoading(false);
  }

  /**
   * 发送消息到Service Worker
   */
  async sendMessage(message) {
    if (typeof chrome === 'undefined' || !chrome.runtime) {
      return { success: false, error: 'Chrome runtime not available' };
    }
    
    return new Promise((resolve) => {
      chrome.runtime.sendMessage(message, (response) => {
        resolve(response || { success: false, error: 'No response' });
      });
    });
  }

  /**
   * 处理搜索
   */
  handleSearch(searchTerm) {
    this.searchTerm = searchTerm.toLowerCase().trim();
    this.updateSearchUI();
    this.filterData();
    this.render();
  }

  /**
   * 清除搜索
   */
  clearSearch() {
    const searchInput = document.getElementById('searchInput');
    if (searchInput) {
      searchInput.value = '';
      this.handleSearch('');
    }
  }

  /**
   * 更新搜索UI
   */
  updateSearchUI() {
    const clearBtn = document.getElementById('clearSearchBtn');
    if (clearBtn) {
      clearBtn.classList.toggle('visible', this.searchTerm.length > 0);
    }
  }

  /**
   * 处理筛选
   */
  handleFilter(filter) {
    this.currentFilter = filter;
    
    // 更新筛选按钮状态
    document.querySelectorAll('.filter-btn').forEach(btn => {
      btn.classList.toggle('active', btn.dataset.filter === filter);
    });
    
    this.filterData();
    this.render();
  }

  /**
   * 筛选数据
   */
  filterData() {
    if (!this.browserData) {
      this.filteredData = null;
      return;
    }

    let filteredWindows = {};
    let filteredTabs = {};
    let filteredGroups = {};

    // 应用搜索筛选
    Object.entries(this.browserData.windows || {}).forEach(([windowId, window]) => {
      const windowMatches = this.matchesSearch(window, 'window');
      const windowTabs = {};
      let hasMatchingTabs = false;

      // 筛选窗口中的标签页
      Object.entries(this.browserData.tabs || {}).forEach(([tabId, tab]) => {
        if (tab.windowId == windowId) {
          const tabMatches = this.matchesSearch(tab, 'tab');
          if (tabMatches) {
            windowTabs[tabId] = tab;
            filteredTabs[tabId] = tab;
            hasMatchingTabs = true;
          }
        }
      });

      // 如果没有搜索条件，或者窗口/标签页匹配，则包含该窗口
      if (!this.searchTerm || windowMatches || hasMatchingTabs) {
        filteredWindows[windowId] = {
          ...window,
          tabs: !this.searchTerm ?
            // 没有搜索条件时，显示该窗口的所有标签页
            Object.fromEntries(
              Object.entries(this.browserData.tabs || {}).filter(([tabId, tab]) => tab.windowId == windowId)
            ) :
            // 有搜索条件时，只显示匹配的标签页
            windowTabs
        };
      }
    });

    // 筛选标签组
    Object.entries(this.browserData.tabGroups || {}).forEach(([groupId, group]) => {
      if (this.matchesSearch(group, 'group')) {
        filteredGroups[groupId] = group;
      }
    });

    // 应用类型筛选
    if (this.currentFilter !== 'all') {
      if (this.currentFilter === 'tabs') {
        filteredWindows = {};
        filteredGroups = {};
      } else if (this.currentFilter === 'windows') {
        filteredTabs = {};
        filteredGroups = {};
      } else if (this.currentFilter === 'groups') {
        filteredWindows = {};
        filteredTabs = {};
      }
    }

    this.filteredData = {
      windows: filteredWindows,
      tabs: filteredTabs,
      tabGroups: filteredGroups,
      settings: this.browserData.settings
    };
  }

  /**
   * 检查项目是否匹配搜索条件
   */
  matchesSearch(item, type) {
    if (!this.searchTerm) return true;

    const searchFields = [];

    if (type === 'tab') {
      searchFields.push(
        item.title || '',
        item.url || '',
        item.notes || '',
        ...(item.tags || [])
      );
    } else if (type === 'window') {
      searchFields.push(
        item.name || '',
        item.description || ''
      );
    } else if (type === 'group') {
      searchFields.push(
        item.title || '',
        item.description || '',
        ...(item.tags || [])
      );
    }

    return searchFields.some(field =>
      field.toLowerCase().includes(this.searchTerm)
    );
  }

  /**
   * 渲染界面
   */
  render() {
    this.renderStats();
    this.renderContent();
    this.updateBatchToolbar();
    this.updateWindowCheckboxes();
  }

  /**
   * 更新窗口复选框状态
   */
  updateWindowCheckboxes() {
    // 延迟执行，确保DOM已更新
    setTimeout(() => {
      document.querySelectorAll('.window-checkbox').forEach(checkbox => {
        const windowId = checkbox.closest('.window-item')?.dataset.windowId;
        if (!windowId) return;

        const windowTabs = Object.entries(this.filteredData.tabs || {})
          .filter(([tabId, tab]) => tab.windowId == windowId);

        const selectedCount = windowTabs.filter(([tabId]) => this.selectedTabs.has(tabId)).length;
        const totalCount = windowTabs.length;

        if (selectedCount === 0) {
          checkbox.checked = false;
          checkbox.indeterminate = false;
          checkbox.removeAttribute('data-indeterminate');
        } else if (selectedCount === totalCount) {
          checkbox.checked = true;
          checkbox.indeterminate = false;
          checkbox.removeAttribute('data-indeterminate');
        } else {
          checkbox.checked = false;
          checkbox.indeterminate = true;
          checkbox.setAttribute('data-indeterminate', 'true');
        }
      });
    }, 0);
  }

  /**
   * 渲染统计信息
   */
  renderStats() {
    const totalTabsEl = document.getElementById('totalTabs');
    const totalWindowsEl = document.getElementById('totalWindows');
    const totalGroupsEl = document.getElementById('totalGroups');
    const totalMemoryEl = document.getElementById('totalMemory');

    if (!this.filteredData) {
      totalTabsEl.textContent = '0';
      totalWindowsEl.textContent = '0';
      totalGroupsEl.textContent = '0';
      totalMemoryEl.textContent = '0MB';
      return;
    }

    const tabCount = Object.keys(this.filteredData.tabs || {}).length;
    const windowCount = Object.keys(this.filteredData.windows || {}).length;
    const groupCount = Object.keys(this.filteredData.tabGroups || {}).length;
    const estimatedMemory = tabCount * 50; // 估算每个标签页50MB

    totalTabsEl.textContent = tabCount;
    totalWindowsEl.textContent = windowCount;
    totalGroupsEl.textContent = groupCount;
    totalMemoryEl.textContent = `${estimatedMemory}MB`;
  }

  /**
   * 渲染主要内容
   */
  renderContent() {
    const windowsList = document.getElementById('windowsList');
    const tabsList = document.getElementById('tabsList');
    const emptyState = document.getElementById('emptyState');

    // 隐藏所有列表
    windowsList.style.display = 'none';
    tabsList.style.display = 'none';
    emptyState.style.display = 'none';

    if (!this.filteredData) {
      emptyState.style.display = 'flex';
      return;
    }

    // 根据当前筛选类型显示不同内容
    if (this.currentFilter === 'tabs') {
      this.renderTabsList();
    } else if (this.currentFilter === 'all' || this.currentFilter === 'windows') {
      this.renderWindowsList();
    } else {
      emptyState.style.display = 'flex';
    }
  }

  /**
   * 渲染窗口列表
   */
  renderWindowsList() {
    const windowsList = document.getElementById('windowsList');
    const emptyState = document.getElementById('emptyState');

    if (Object.keys(this.filteredData.windows || {}).length === 0) {
      emptyState.style.display = 'flex';
      return;
    }

    windowsList.style.display = 'block';
    windowsList.innerHTML = '';

    // 渲染每个窗口
    Object.entries(this.filteredData.windows).forEach(([windowId, window]) => {
      const windowElement = this.createWindowElement(windowId, window);
      windowsList.appendChild(windowElement);
    });
  }

  /**
   * 渲染标签页列表
   */
  renderTabsList() {
    const tabsList = document.getElementById('tabsList');
    const emptyState = document.getElementById('emptyState');
    const tabsListCount = document.getElementById('tabsListCount');

    const allTabs = this.filteredData.tabs || {};
    const tabCount = Object.keys(allTabs).length;

    if (tabCount === 0) {
      emptyState.style.display = 'flex';
      return;
    }

    tabsList.style.display = 'block';

    // 更新计数显示
    if (tabsListCount) {
      tabsListCount.textContent = `${tabCount} 个标签页`;
    }

    const tabsContainer = tabsList.querySelector('.tabs-container');
    tabsContainer.innerHTML = this.createTabsHTML(allTabs);
  }

  /**
   * 创建窗口元素
   */
  createWindowElement(windowId, window) {
    const windowDiv = document.createElement('div');
    windowDiv.className = 'window-item';
    windowDiv.dataset.windowId = windowId;

    const tabCount = Object.keys(window.tabs || {}).length;
    const windowTitle = window.name || `窗口 ${windowId}`;
    const windowMeta = `${tabCount} 个标签页`;

    const windowTabsArray = Object.entries(window.tabs || {});
    const allSelected = windowTabsArray.length > 0 && windowTabsArray.every(([tabId]) => this.selectedTabs.has(tabId));
    const someSelected = windowTabsArray.some(([tabId]) => this.selectedTabs.has(tabId));

    windowDiv.innerHTML = `
      <div class="window-header expanded">
        <div class="window-info" onclick="window.tabManagerApp.toggleWindow('${windowId}')">
          <input type="checkbox" class="window-checkbox"
                 ${allSelected ? 'checked' : ''}
                 ${someSelected && !allSelected ? 'data-indeterminate="true"' : ''}
                 onchange="window.tabManagerApp.toggleWindowSelection('${windowId}', this.checked)"
                 onclick="event.stopPropagation()" title="选择/取消选择窗口所有标签页">
          <svg class="window-expand-icon" viewBox="0 0 24 24" fill="currentColor">
            <path d="M8.59 16.59L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z"/>
          </svg>
          <svg class="window-icon" viewBox="0 0 24 24" fill="currentColor">
            <path d="M4 6H2v14c0 1.1.9 2 2 2h14v-2H4V6zm16-4H8c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm-1 9H9V9h10v2zm-4 4H9v-2h6v2zm4-8H9V5h10v2z"/>
          </svg>
          <div>
            <div class="window-title">${this.escapeHtml(windowTitle)}</div>
            <div class="window-meta">${windowMeta}</div>
          </div>
        </div>
        <div class="window-actions">
          <button class="btn btn-secondary" onclick="event.stopPropagation(); window.tabManagerApp.openSelectedInNewWindow('${windowId}')" title="在新窗口打开选中标签页">
            <svg viewBox="0 0 24 24" fill="currentColor">
              <path d="M19 19H5V5h7V3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2v-7h-2v7zM14 3v2h3.59l-9.83 9.83 1.41 1.41L19 6.41V10h2V3h-7z"/>
            </svg>
            新窗口
          </button>
          <button class="btn btn-secondary" onclick="event.stopPropagation(); window.tabManagerApp.openSelectedInCurrentWindow('${windowId}')" title="在当前窗口打开选中标签页">
            <svg viewBox="0 0 24 24" fill="currentColor">
              <path d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z"/>
            </svg>
            当前窗口
          </button>
          <button class="btn btn-secondary" onclick="event.stopPropagation(); window.tabManagerApp.suspendSelectedTabs('${windowId}')" title="休眠选中标签页">
            <svg viewBox="0 0 24 24" fill="currentColor">
              <path d="M6 19h4V5H6v14zm8-14v14h4V5h-4z"/>
            </svg>
            休眠
          </button>
          <button class="btn btn-secondary" onclick="event.stopPropagation(); window.tabManagerApp.createGroupFromSelected('${windowId}')" title="将选中标签页创建分组">
            <svg viewBox="0 0 24 24" fill="currentColor">
              <path d="M22 16V4c0-1.1-.9-2-2-2H8c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2zm-11.5-9L15 10.5 13.5 12 10 8.5 8.5 10 7 8.5 10.5 5z"/>
            </svg>
            分组
          </button>
          <div class="window-sort-actions">
            <button class="btn btn-icon" onclick="event.stopPropagation(); window.tabManagerApp.moveWindowUp('${windowId}')" title="上移窗口">
              <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M7.41 15.41L12 10.83l4.59 4.58L18 14l-6-6-6 6z"/>
              </svg>
            </button>
            <button class="btn btn-icon" onclick="event.stopPropagation(); window.tabManagerApp.moveWindowDown('${windowId}')" title="下移窗口">
              <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M7.41 8.59L12 13.17l4.59-4.58L18 10l-6 6-6-6z"/>
              </svg>
            </button>
            <button class="btn btn-icon" onclick="event.stopPropagation(); window.tabManagerApp.moveWindowToTop('${windowId}')" title="置顶窗口">
              <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M4 12l1.41 1.41L11 7.83V20h2V7.83l5.58 5.59L20 12l-8-8-8 8z"/>
              </svg>
            </button>
            <button class="btn btn-icon" onclick="event.stopPropagation(); window.tabManagerApp.moveWindowToBottom('${windowId}')" title="置底窗口">
              <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M20 12l-1.41-1.41L13 16.17V4h-2v12.17l-5.58-5.59L4 12l8 8 8-8z"/>
              </svg>
            </button>
          </div>
        </div>
      </div>
      <div class="window-tabs" id="windowTabs-${windowId}" style="display: block;">
        <div class="tabs-container">
          ${this.createTabsHTML(window.tabs || {})}
        </div>
      </div>
    `;

    return windowDiv;
  }

  /**
   * 创建标签页HTML - 横向布局
   */
  createTabsHTML(tabs) {
    return Object.entries(tabs).map(([tabId, tab]) => {
      const favicon = this.getFaviconUrl(tab.favicon, tab.url);
      const isActive = tab.active ? 'active' : '';
      const isSelected = this.selectedTabs.has(tabId) ? 'checked' : '';

      return `
        <div class="tab-item ${isActive}" data-tab-id="${tabId}">
          <input type="checkbox" class="tab-checkbox" ${isSelected}
                 onchange="window.tabManagerApp.toggleTabSelection('${tabId}', this.checked)"
                 onclick="event.stopPropagation()">
          <img class="tab-favicon" src="${favicon}" alt=""
               onerror="this.src='data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCI+PHBhdGggZmlsbD0iIzY2NiIgZD0iTTEyIDJDNi40OCAyIDIgNi40OCAyIDEyczQuNDggMTAgMTAgMTAgMTAtNC40OCAxMC0xMFMxNy41MiAyIDEyIDJ6Ii8+PC9zdmc+'">
          <div class="tab-info" onclick="window.tabManagerApp.activateTab('${tabId}')">
            <div class="tab-title">${this.escapeHtml(tab.title || '无标题')}</div>
            <div class="tab-url">${this.escapeHtml(this.formatUrl(tab.url || ''))}</div>
          </div>
          <div class="tab-actions">
            <div class="tab-close" onclick="event.stopPropagation(); window.tabManagerApp.closeTab('${tabId}')" title="关闭标签页">
              <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
              </svg>
            </div>
          </div>
        </div>
      `;
    }).join('');
  }

  /**
   * 获取安全的favicon URL
   */
  getFaviconUrl(favicon, url) {
    if (favicon && favicon.startsWith('http')) {
      return favicon;
    }

    if (url) {
      try {
        const urlObj = new URL(url);
        return `${urlObj.protocol}//${urlObj.hostname}/favicon.ico`;
      } catch (e) {
        // URL解析失败，使用默认图标
      }
    }

    // 返回base64编码的默认SVG图标
    return 'data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCI+PHBhdGggZmlsbD0iIzY2NiIgZD0iTTEyIDJDNi40OCAyIDIgNi40OCAyIDEyczQuNDggMTAgMTAgMTAgMTAtNC40OCAxMC0xMFMxNy41MiAyIDEyIDJ6Ii8+PC9zdmc+';
  }

  /**
   * 显示/隐藏加载状态
   */
  showLoading(show) {
    const loadingState = document.getElementById('loadingState');
    const mainContent = document.querySelector('.main-content');

    if (show) {
      loadingState.style.display = 'flex';
    } else {
      loadingState.style.display = 'none';
    }

    this.isLoading = show;
  }

  /**
   * 显示错误信息
   */
  showError(message) {
    this.showNotification(message, 'error');
  }

  /**
   * 显示通知
   */
  showNotification(message, type = 'info') {
    const container = document.getElementById('notificationContainer');
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.innerHTML = `
      <div class="notification-content">
        <span>${message}</span>
        <button class="notification-close" onclick="this.parentElement.parentElement.remove()">×</button>
      </div>
    `;

    container.appendChild(notification);

    // 自动移除通知
    setTimeout(() => {
      if (notification.parentNode) {
        notification.parentNode.removeChild(notification);
      }
    }, 5000);
  }

  /**
   * 转义HTML
   */
  escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
  }

  /**
   * 格式化URL显示
   */
  formatUrl(url) {
    try {
      const urlObj = new URL(url);
      return urlObj.hostname + urlObj.pathname;
    } catch {
      return url;
    }
  }

  // 事件处理方法

  /**
   * 切换窗口展开/折叠
   */
  toggleWindow(windowId) {
    const tabsContainer = document.getElementById(`windowTabs-${windowId}`);
    const windowElement = document.querySelector(`[data-window-id="${windowId}"]`);
    const windowHeader = windowElement?.querySelector('.window-header');

    if (tabsContainer && windowHeader) {
      const isExpanded = windowHeader.classList.contains('expanded');

      if (isExpanded) {
        tabsContainer.style.display = 'none';
        windowHeader.classList.remove('expanded');
      } else {
        tabsContainer.style.display = 'block';
        windowHeader.classList.add('expanded');
      }
    }
  }

  /**
   * 激活标签页
   */
  async activateTab(tabId) {
    try {
      if (typeof chrome !== 'undefined' && chrome.runtime) {
        const response = await this.sendMessage({
          action: 'ACTIVATE_TAB',
          data: { tabId: parseInt(tabId) }
        });

        if (response.success) {
          this.showNotification('标签页已激活', 'success');
        } else {
          this.showError('激活标签页失败');
        }
      } else {
        this.showNotification('标签页已激活（演示模式）', 'info');
      }
    } catch (error) {
      console.error('Failed to activate tab:', error);
      this.showError('激活标签页失败');
    }
  }

  /**
   * 关闭标签页
   */
  async closeTab(tabId) {
    try {
      if (typeof chrome !== 'undefined' && chrome.runtime) {
        const response = await this.sendMessage({
          action: 'CLOSE_TABS',
          data: { tabIds: [parseInt(tabId)] }
        });

        if (response.success) {
          this.showNotification('标签页已关闭', 'success');
          // 重新加载数据
          await this.loadBrowserData();
          this.render();
        } else {
          this.showError('关闭标签页失败');
        }
      } else {
        // 演示模式 - 从数据中移除
        delete this.browserData.tabs[tabId];
        this.filterData();
        this.render();
        this.showNotification('标签页已关闭（演示模式）', 'info');
      }
    } catch (error) {
      console.error('Failed to close tab:', error);
      this.showError('关闭标签页失败');
    }
  }

  /**
   * 聚焦窗口
   */
  async focusWindow(windowId) {
    try {
      if (typeof chrome !== 'undefined' && chrome.runtime) {
        const response = await this.sendMessage({
          action: 'FOCUS_WINDOW',
          data: { windowId: parseInt(windowId) }
        });

        if (response.success) {
          this.showNotification('Window focused', 'success');
        } else {
          this.showError('Failed to focus window');
        }
      } else {
        this.showNotification('Window focused (demo mode)', 'info');
      }
    } catch (error) {
      console.error('Failed to focus window:', error);
      this.showError('Failed to focus window');
    }
  }

  /**
   * 显示窗口菜单
   */
  showWindowMenu(windowId, event) {
    event.preventDefault();
    event.stopPropagation();

    const contextMenu = document.getElementById('contextMenu');
    contextMenu.innerHTML = `
      <div class="context-menu-item" onclick="tabManagerApp.newTabInWindow('${windowId}')">
        <svg viewBox="0 0 24 24" fill="currentColor">
          <path d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z"/>
        </svg>
        新建标签页
      </div>
      <div class="context-menu-item" onclick="tabManagerApp.closeWindow('${windowId}')">
        <svg viewBox="0 0 24 24" fill="currentColor">
          <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
        </svg>
        关闭窗口
      </div>
    `;

    contextMenu.style.display = 'block';
    contextMenu.style.left = event.pageX + 'px';
    contextMenu.style.top = event.pageY + 'px';
  }

  /**
   * 隐藏上下文菜单
   */
  hideContextMenu() {
    const contextMenu = document.getElementById('contextMenu');
    contextMenu.style.display = 'none';
  }

  /**
   * 在窗口中创建新标签页
   */
  async newTabInWindow(windowId) {
    try {
      if (typeof chrome !== 'undefined' && chrome.runtime) {
        const response = await this.sendMessage({
          action: 'CREATE_TAB',
          data: {
            windowId: parseInt(windowId),
            url: 'chrome://newtab/'
          }
        });

        if (response.success) {
          this.showNotification('新标签页已创建', 'success');
          await this.loadBrowserData();
          this.render();
        } else {
          this.showError('创建新标签页失败');
        }
      } else {
        this.showNotification('新标签页已创建（演示模式）', 'info');
      }
    } catch (error) {
      console.error('Failed to create new tab:', error);
      this.showError('创建新标签页失败');
    }

    this.hideContextMenu();
  }

  /**
   * 关闭窗口
   */
  async closeWindow(windowId) {
    try {
      if (typeof chrome !== 'undefined' && chrome.runtime) {
        const response = await this.sendMessage({
          action: 'CLOSE_WINDOW',
          data: { windowId: parseInt(windowId) }
        });

        if (response.success) {
          this.showNotification('Window closed', 'success');
          await this.loadBrowserData();
          this.render();
        } else {
          this.showError('Failed to close window');
        }
      } else {
        // 演示模式 - 从数据中移除
        delete this.browserData.windows[windowId];
        this.filterData();
        this.render();
        this.showNotification('Window closed (demo mode)', 'info');
      }
    } catch (error) {
      console.error('Failed to close window:', error);
      this.showError('Failed to close window');
    }

    this.hideContextMenu();
  }

  /**
   * 处理同步
   */
  async handleSync() {
    try {
      this.showLoading(true);

      if (typeof chrome !== 'undefined' && chrome.runtime) {
        // 从Chrome扩展加载数据
        await this.loadBrowserData();
        this.render();
        this.showNotification('浏览器数据加载成功', 'success');
      } else {
        // 演示模式 - 加载演示数据
        this.loadDemoData();
        this.render();
        this.showNotification('演示数据已加载', 'info');
      }
    } catch (error) {
      console.error('Sync failed:', error);
      this.showError('数据加载失败');
    } finally {
      this.showLoading(false);
    }
  }

  /**
   * 处理设置
   */
  handleSettings() {
    this.showNotification('设置功能即将推出', 'info');
  }

  /**
   * 切换全屏模式
   */
  toggleFullscreen() {
    if (!document.fullscreenElement) {
      document.documentElement.requestFullscreen();
      this.isFullscreen = true;
    } else {
      document.exitFullscreen();
      this.isFullscreen = false;
    }
  }

  /**
   * 处理新窗口
   */
  async handleNewWindow() {
    try {
      if (typeof chrome !== 'undefined' && chrome.runtime) {
        const response = await this.sendMessage({
          action: 'CREATE_WINDOW',
          data: { url: 'chrome://newtab/' }
        });

        if (response.success) {
          this.showNotification('新窗口已创建', 'success');
          await this.loadBrowserData();
          this.render();
        } else {
          this.showError('创建新窗口失败');
        }
      } else {
        this.showNotification('新窗口已创建（演示模式）', 'info');
      }
    } catch (error) {
      console.error('Failed to create new window:', error);
      this.showError('创建新窗口失败');
    }
  }

  /**
   * 处理导出
   */
  handleExport() {
    // 显示导出格式选择对话框
    const format = prompt('请选择导出格式：\n1. JSON格式（输入 json）\n2. HTML格式（输入 html）', 'json');

    if (!format) return;

    if (format.toLowerCase() === 'html') {
      this.exportToHTML();
    } else {
      this.exportToJSON();
    }
  }

  /**
   * 导出为JSON格式
   */
  exportToJSON() {
    try {
      const exportData = {
        version: '1.0.0',
        exportTime: new Date().toISOString(),
        data: {
          tabs: this.browserData?.tabs || {},
          windows: this.browserData?.windows || {},
          tabGroups: this.browserData?.tabGroups || {},
          settings: this.browserData?.settings || {}
        }
      };

      const jsonString = JSON.stringify(exportData, null, 2);
      const blob = new Blob([jsonString], { type: 'application/json' });
      const url = URL.createObjectURL(blob);

      const a = document.createElement('a');
      a.href = url;
      a.download = `tab-manager-export-${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);

      this.showNotification('JSON数据导出成功', 'success');
    } catch (error) {
      console.error('JSON export failed:', error);
      this.showError('JSON导出失败');
    }
  }

  /**
   * 导出为HTML格式
   */
  exportToHTML() {
    try {
      const tabs = this.browserData?.tabs || {};
      const windows = this.browserData?.windows || {};

      let htmlContent = `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>书签式标签页管理器 - 导出数据</title>
    <style>
        body { font-family: 'Segoe UI', sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        h1 { color: #4285f4; text-align: center; margin-bottom: 30px; }
        .export-info { background: #e3f2fd; padding: 15px; border-radius: 5px; margin-bottom: 20px; }
        .window-section { margin-bottom: 30px; border: 1px solid #e0e0e0; border-radius: 8px; overflow: hidden; }
        .window-header { background: #4285f4; color: white; padding: 15px; font-weight: bold; font-size: 16px; }
        .tabs-grid { display: grid; grid-template-columns: repeat(auto-fill, minmax(300px, 1fr)); gap: 15px; padding: 20px; }
        .tab-card { border: 1px solid #e0e0e0; border-radius: 6px; padding: 15px; background: #fafafa; transition: box-shadow 0.2s; }
        .tab-card:hover { box-shadow: 0 2px 8px rgba(0,0,0,0.1); }
        .tab-title { font-weight: 500; color: #333; margin-bottom: 8px; word-break: break-word; }
        .tab-url { color: #666; font-size: 14px; word-break: break-all; }
        .tab-favicon { width: 16px; height: 16px; margin-right: 8px; vertical-align: middle; }
        .stats { display: flex; gap: 20px; justify-content: center; margin-bottom: 20px; }
        .stat-item { text-align: center; padding: 10px 20px; background: #f0f0f0; border-radius: 5px; }
        .stat-number { font-size: 24px; font-weight: bold; color: #4285f4; }
        .stat-label { font-size: 14px; color: #666; }
    </style>
</head>
<body>
    <div class="container">
        <h1>📚 书签式标签页管理器</h1>

        <div class="export-info">
            <strong>导出时间：</strong>${new Date().toLocaleString('zh-CN')}<br>
            <strong>导出格式：</strong>HTML书签格式
        </div>

        <div class="stats">
            <div class="stat-item">
                <div class="stat-number">${Object.keys(tabs).length}</div>
                <div class="stat-label">标签页</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">${Object.keys(windows).length}</div>
                <div class="stat-label">窗口</div>
            </div>
        </div>
`;

      // 按窗口分组显示标签页
      Object.entries(windows).forEach(([windowId, window]) => {
        const windowTabs = Object.entries(tabs).filter(([tabId, tab]) => tab.windowId == windowId);

        if (windowTabs.length > 0) {
          htmlContent += `
        <div class="window-section">
            <div class="window-header">
                🪟 ${this.escapeHtml(window.name || `窗口 ${windowId}`)} (${windowTabs.length} 个标签页)
            </div>
            <div class="tabs-grid">`;

          windowTabs.forEach(([tabId, tab]) => {
            const favicon = this.getFaviconUrl(tab.favicon, tab.url);
            htmlContent += `
                <div class="tab-card">
                    <div class="tab-title">
                        <img class="tab-favicon" src="${favicon}" alt="" onerror="this.style.display='none'">
                        <a href="${this.escapeHtml(tab.url || '#')}" target="_blank" style="text-decoration: none; color: inherit;">
                            ${this.escapeHtml(tab.title || '无标题')}
                        </a>
                    </div>
                    <div class="tab-url">${this.escapeHtml(this.formatUrl(tab.url || ''))}</div>
                </div>`;
          });

          htmlContent += `
            </div>
        </div>`;
        }
      });

      htmlContent += `
    </div>
</body>
</html>`;

      const blob = new Blob([htmlContent], { type: 'text/html; charset=utf-8' });
      const url = URL.createObjectURL(blob);

      const a = document.createElement('a');
      a.href = url;
      a.download = `tab-manager-bookmarks-${new Date().toISOString().split('T')[0]}.html`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);

      this.showNotification('HTML书签导出成功', 'success');
    } catch (error) {
      console.error('HTML export failed:', error);
      this.showError('HTML导出失败');
    }
  }

  /**
   * 处理导入
   */
  handleImport() {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.json,.html';
    input.onchange = (e) => {
      const file = e.target.files[0];
      if (!file) return;

      const fileExtension = file.name.split('.').pop().toLowerCase();

      if (fileExtension === 'html') {
        this.importFromHTML(file);
      } else {
        this.importFromJSON(file);
      }
    };
    input.click();
  }

  /**
   * 从JSON文件导入
   */
  importFromJSON(file) {
    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const importData = JSON.parse(e.target.result);

        // 验证导入数据格式
        if (!importData.version || !importData.data) {
          throw new Error('无效的JSON导入文件格式');
        }

        // 确认导入
        if (confirm('导入JSON数据将覆盖当前数据，确定要继续吗？')) {
          this.browserData = {
            tabs: importData.data.tabs || {},
            windows: importData.data.windows || {},
            tabGroups: importData.data.tabGroups || {},
            settings: importData.data.settings || {}
          };

          this.filterData();
          this.render();
          this.showNotification('JSON数据导入成功', 'success');
        }
      } catch (error) {
        console.error('JSON import failed:', error);
        this.showError('JSON导入失败：' + error.message);
      }
    };
    reader.readAsText(file);
  }

  /**
   * 从HTML文件导入
   */
  importFromHTML(file) {
    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const htmlContent = e.target.result;
        const parser = new DOMParser();
        const doc = parser.parseFromString(htmlContent, 'text/html');

        // 解析HTML中的链接
        const links = doc.querySelectorAll('a[href]');
        const importedTabs = {};
        const importedWindows = {};

        let tabId = 1;
        let windowId = 1;

        // 创建默认窗口
        importedWindows[windowId] = {
          id: windowId,
          name: '导入的书签',
          focused: true
        };

        links.forEach((link, index) => {
          const href = link.getAttribute('href');
          const title = link.textContent.trim();

          if (href && href !== '#' && title) {
            importedTabs[tabId] = {
              id: tabId,
              title: title,
              url: href,
              windowId: windowId,
              active: index === 0,
              pinned: false,
              audible: false,
              incognito: false,
              createdAt: new Date().toISOString(),
              lastAccessed: new Date().toISOString()
            };
            tabId++;
          }
        });

        if (Object.keys(importedTabs).length === 0) {
          throw new Error('HTML文件中未找到有效的链接');
        }

        // 确认导入
        if (confirm(`从HTML文件中找到 ${Object.keys(importedTabs).length} 个链接，确定要导入吗？`)) {
          // 合并到现有数据
          this.browserData = {
            tabs: { ...this.browserData?.tabs, ...importedTabs },
            windows: { ...this.browserData?.windows, ...importedWindows },
            tabGroups: this.browserData?.tabGroups || {},
            settings: this.browserData?.settings || {}
          };

          this.filterData();
          this.render();
          this.showNotification(`HTML书签导入成功，导入了 ${Object.keys(importedTabs).length} 个链接`, 'success');
        }
      } catch (error) {
        console.error('HTML import failed:', error);
        this.showError('HTML导入失败：' + error.message);
      }
    };
    reader.readAsText(file);
  }

  /**
   * 处理查找重复标签页
   */
  handleFindDuplicates() {
    this.showNotification('查找重复功能即将推出', 'info');
  }

  // 批量操作相关方法

  /**
   * 切换标签页选择状态
   */
  toggleTabSelection(tabId, checked) {
    if (checked) {
      this.selectedTabs.add(tabId);
    } else {
      this.selectedTabs.delete(tabId);
    }
    this.updateBatchToolbar();
    this.updateWindowCheckboxes();
  }

  /**
   * 清除所有选择
   */
  clearSelection() {
    this.selectedTabs.clear();
    // 取消所有复选框的选中状态
    document.querySelectorAll('.tab-checkbox').forEach(checkbox => {
      checkbox.checked = false;
    });
    this.updateBatchToolbar();
  }

  /**
   * 更新批量操作工具栏
   */
  updateBatchToolbar() {
    const toolbar = document.getElementById('batchToolbar');
    const selectedCount = document.getElementById('selectedCount');

    if (this.selectedTabs.size > 0) {
      toolbar.classList.add('visible');
      selectedCount.textContent = this.selectedTabs.size;
    } else {
      toolbar.classList.remove('visible');
    }
  }

  /**
   * 批量打开标签页
   */
  async handleBatchOpen() {
    if (this.selectedTabs.size === 0) {
      this.showNotification('请先选择要打开的标签页', 'warning');
      return;
    }

    try {
      for (const tabId of this.selectedTabs) {
        await this.activateTab(tabId);
        // 添加小延迟避免过快操作
        await new Promise(resolve => setTimeout(resolve, 100));
      }
      this.showNotification(`已打开 ${this.selectedTabs.size} 个标签页`, 'success');
      this.clearSelection();
    } catch (error) {
      this.showError('批量打开失败');
    }
  }

  /**
   * 批量关闭标签页
   */
  async handleBatchClose() {
    if (this.selectedTabs.size === 0) {
      this.showNotification('请先选择要关闭的标签页', 'warning');
      return;
    }

    if (!confirm(`确定要关闭选中的 ${this.selectedTabs.size} 个标签页吗？`)) {
      return;
    }

    try {
      const tabIds = Array.from(this.selectedTabs);

      if (typeof chrome !== 'undefined' && chrome.runtime) {
        const response = await this.sendMessage({
          action: 'CLOSE_TABS',
          data: { tabIds: tabIds.map(id => parseInt(id)) }
        });

        if (response.success) {
          this.showNotification(`已关闭 ${tabIds.length} 个标签页`, 'success');
          await this.loadBrowserData();
          this.render();
        } else {
          this.showError('批量关闭失败');
        }
      } else {
        // 演示模式
        tabIds.forEach(tabId => {
          delete this.browserData.tabs[tabId];
        });
        this.filterData();
        this.render();
        this.showNotification(`已关闭 ${tabIds.length} 个标签页（演示模式）`, 'info');
      }

      this.clearSelection();
    } catch (error) {
      this.showError('批量关闭失败');
    }
  }

  /**
   * 批量创建分组
   */
  handleBatchGroup() {
    if (this.selectedTabs.size === 0) {
      this.showNotification('请先选择要分组的标签页', 'warning');
      return;
    }

    const groupName = prompt('请输入分组名称：');
    if (!groupName) return;

    this.showNotification(`已将 ${this.selectedTabs.size} 个标签页添加到分组"${groupName}"（演示模式）`, 'info');
    this.clearSelection();
  }

  /**
   * 批量添加书签
   */
  handleBatchBookmark() {
    if (this.selectedTabs.size === 0) {
      this.showNotification('请先选择要添加书签的标签页', 'warning');
      return;
    }

    this.showNotification(`已将 ${this.selectedTabs.size} 个标签页添加到书签（演示模式）`, 'info');
    this.clearSelection();
  }

  // 全选功能相关方法

  /**
   * 选择指定窗口的所有标签页
   */
  selectAllWindowTabs(windowId) {
    const windowTabs = Object.entries(this.filteredData.tabs || {})
      .filter(([tabId, tab]) => tab.windowId == windowId);

    windowTabs.forEach(([tabId, tab]) => {
      this.selectedTabs.add(tabId);
    });

    // 更新复选框状态
    windowTabs.forEach(([tabId, tab]) => {
      const checkbox = document.querySelector(`[data-tab-id="${tabId}"] .tab-checkbox`);
      if (checkbox) checkbox.checked = true;
    });

    this.updateBatchToolbar();
    this.showNotification(`已选择窗口中的 ${windowTabs.length} 个标签页`, 'success');
  }

  /**
   * 取消选择指定窗口的所有标签页
   */
  deselectAllWindowTabs(windowId) {
    const windowTabs = Object.entries(this.filteredData.tabs || {})
      .filter(([tabId, tab]) => tab.windowId == windowId);

    windowTabs.forEach(([tabId, tab]) => {
      this.selectedTabs.delete(tabId);
    });

    // 更新复选框状态
    windowTabs.forEach(([tabId, tab]) => {
      const checkbox = document.querySelector(`[data-tab-id="${tabId}"] .tab-checkbox`);
      if (checkbox) checkbox.checked = false;
    });

    this.updateBatchToolbar();
    this.showNotification(`已取消选择窗口中的 ${windowTabs.length} 个标签页`, 'success');
  }

  /**
   * 选择所有标签页
   */
  selectAllTabs() {
    const allTabs = Object.keys(this.filteredData.tabs || {});

    allTabs.forEach(tabId => {
      this.selectedTabs.add(tabId);
    });

    // 更新所有复选框状态
    document.querySelectorAll('.tab-checkbox').forEach(checkbox => {
      checkbox.checked = true;
    });

    this.updateBatchToolbar();
    this.showNotification(`已选择所有 ${allTabs.length} 个标签页`, 'success');
  }

  /**
   * 取消选择所有标签页
   */
  deselectAllTabs() {
    const count = this.selectedTabs.size;
    this.selectedTabs.clear();

    // 更新所有复选框状态
    document.querySelectorAll('.tab-checkbox').forEach(checkbox => {
      checkbox.checked = false;
    });

    this.updateBatchToolbar();
    this.showNotification(`已取消选择 ${count} 个标签页`, 'success');
  }

  /**
   * 切换全选状态
   */
  toggleSelectAllTabs() {
    const allTabs = Object.keys(this.filteredData.tabs || {});
    const allSelected = allTabs.every(tabId => this.selectedTabs.has(tabId));

    if (allSelected) {
      this.deselectAllTabs();
    } else {
      this.selectAllTabs();
    }
  }

  /**
   * 切换窗口选择状态
   */
  toggleWindowSelection(windowId, checked) {
    const windowTabs = Object.entries(this.filteredData.tabs || {})
      .filter(([tabId, tab]) => tab.windowId == windowId);

    if (checked) {
      // 选择窗口所有标签页
      windowTabs.forEach(([tabId]) => {
        this.selectedTabs.add(tabId);
      });
    } else {
      // 取消选择窗口所有标签页
      windowTabs.forEach(([tabId]) => {
        this.selectedTabs.delete(tabId);
      });
    }

    // 更新标签页复选框状态
    windowTabs.forEach(([tabId]) => {
      const checkbox = document.querySelector(`[data-tab-id="${tabId}"] .tab-checkbox`);
      if (checkbox) checkbox.checked = checked;
    });

    this.updateBatchToolbar();
  }

  // 窗口操作功能

  /**
   * 在新窗口打开选中标签页
   */
  async openSelectedInNewWindow(windowId) {
    const selectedInWindow = this.getSelectedTabsInWindow(windowId);
    if (selectedInWindow.length === 0) {
      this.showNotification('请先选择要在新窗口打开的标签页', 'warning');
      return;
    }

    try {
      if (typeof chrome !== 'undefined' && chrome.runtime) {
        // 创建新窗口并打开选中的标签页
        const urls = selectedInWindow.map(tabId => {
          const tab = this.filteredData.tabs[tabId];
          return tab ? tab.url : 'chrome://newtab/';
        });

        const response = await this.sendMessage({
          action: 'CREATE_WINDOW',
          data: { urls: urls }
        });

        if (response.success) {
          this.showNotification(`已在新窗口打开 ${selectedInWindow.length} 个标签页`, 'success');
        } else {
          this.showError('在新窗口打开失败');
        }
      } else {
        this.showNotification(`已在新窗口打开 ${selectedInWindow.length} 个标签页（演示模式）`, 'info');
      }
    } catch (error) {
      this.showError('在新窗口打开失败');
    }
  }

  /**
   * 在当前窗口打开选中标签页
   */
  async openSelectedInCurrentWindow(windowId) {
    const selectedInWindow = this.getSelectedTabsInWindow(windowId);
    if (selectedInWindow.length === 0) {
      this.showNotification('请先选择要在当前窗口打开的标签页', 'warning');
      return;
    }

    try {
      if (typeof chrome !== 'undefined' && chrome.runtime) {
        // 在当前窗口打开选中的标签页
        for (const tabId of selectedInWindow) {
          const tab = this.filteredData.tabs[tabId];
          if (tab && tab.url) {
            await this.sendMessage({
              action: 'CREATE_TAB',
              data: {
                windowId: parseInt(windowId),
                url: tab.url
              }
            });
          }
        }

        this.showNotification(`已在当前窗口打开 ${selectedInWindow.length} 个标签页`, 'success');
        await this.loadBrowserData();
        this.render();
      } else {
        this.showNotification(`已在当前窗口打开 ${selectedInWindow.length} 个标签页（演示模式）`, 'info');
      }
    } catch (error) {
      this.showError('在当前窗口打开失败');
    }
  }

  /**
   * 休眠选中标签页
   */
  async suspendSelectedTabs(windowId) {
    const selectedInWindow = this.getSelectedTabsInWindow(windowId);
    if (selectedInWindow.length === 0) {
      this.showNotification('请先选择要休眠的标签页', 'warning');
      return;
    }

    this.showNotification(`已休眠 ${selectedInWindow.length} 个标签页（演示模式）`, 'info');
  }

  /**
   * 从选中标签页创建分组
   */
  async createGroupFromSelected(windowId) {
    const selectedInWindow = this.getSelectedTabsInWindow(windowId);
    if (selectedInWindow.length === 0) {
      this.showNotification('请先选择要分组的标签页', 'warning');
      return;
    }

    const groupName = prompt('请输入分组名称：');
    if (!groupName) return;

    this.showNotification(`已将 ${selectedInWindow.length} 个标签页创建为分组"${groupName}"（演示模式）`, 'info');
  }

  /**
   * 获取指定窗口中选中的标签页
   */
  getSelectedTabsInWindow(windowId) {
    return Array.from(this.selectedTabs).filter(tabId => {
      const tab = this.filteredData.tabs[tabId];
      return tab && tab.windowId == windowId;
    });
  }

  // 窗口排序功能

  /**
   * 上移窗口
   */
  moveWindowUp(windowId) {
    this.showNotification('窗口上移功能（演示模式）', 'info');
  }

  /**
   * 下移窗口
   */
  moveWindowDown(windowId) {
    this.showNotification('窗口下移功能（演示模式）', 'info');
  }

  /**
   * 置顶窗口
   */
  moveWindowToTop(windowId) {
    this.showNotification('窗口置顶功能（演示模式）', 'info');
  }

  /**
   * 置底窗口
   */
  moveWindowToBottom(windowId) {
    this.showNotification('窗口置底功能（演示模式）', 'info');
  }

  /**
   * 处理键盘快捷键
   */
  handleKeyboard(event) {
    // Ctrl/Cmd + F: 聚焦搜索框
    if ((event.ctrlKey || event.metaKey) && event.key === 'f') {
      event.preventDefault();
      const searchInput = document.getElementById('searchInput');
      searchInput?.focus();
    }

    // Escape: 清除搜索或关闭菜单
    if (event.key === 'Escape') {
      const contextMenu = document.getElementById('contextMenu');
      if (contextMenu.style.display === 'block') {
        this.hideContextMenu();
      } else {
        this.clearSearch();
      }
    }

    // F11: 切换全屏
    if (event.key === 'F11') {
      event.preventDefault();
      this.toggleFullscreen();
    }
  }

  /**
   * 处理窗口大小变化
   */
  handleResize() {
    // 响应式布局调整
    const isMobile = window.innerWidth < 768;
    document.body.classList.toggle('mobile', isMobile);
  }
}

// 初始化应用
let tabManagerApp;
document.addEventListener('DOMContentLoaded', () => {
  tabManagerApp = new TabManagerApp();
});
