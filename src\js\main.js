/**
 * 主应用控制器
 * 管理完整的标签页管理界面
 */

class TabManagerApp {
  constructor() {
    this.browserData = null;
    this.filteredData = null;
    this.currentFilter = 'all';
    this.searchTerm = '';
    this.isLoading = true;
    this.isFullscreen = false;
    
    this.init();
  }

  /**
   * 初始化应用
   */
  async init() {
    try {
      console.log('Initializing Tab Manager App...');
      
      // 绑定事件监听器
      this.bindEventListeners();
      
      // 检查是否在扩展环境中
      if (typeof chrome !== 'undefined' && chrome.runtime) {
        // 加载浏览器数据
        await this.loadBrowserData();
      } else {
        // 演示模式，使用模拟数据
        this.loadDemoData();
      }
      
      // 渲染界面
      this.render();
      
      console.log('Tab Manager App initialized successfully');
    } catch (error) {
      console.error('App initialization failed:', error);
      this.showError('标签页管理器初始化失败');
    }
  }

  /**
   * 绑定事件监听器
   */
  bindEventListeners() {
    // 搜索功能
    const searchInput = document.getElementById('searchInput');
    const clearSearchBtn = document.getElementById('clearSearchBtn');
    
    searchInput?.addEventListener('input', (e) => {
      this.handleSearch(e.target.value);
    });
    
    clearSearchBtn?.addEventListener('click', () => {
      this.clearSearch();
    });

    // 筛选按钮
    const filterButtons = document.querySelectorAll('.filter-btn');
    filterButtons.forEach(btn => {
      btn.addEventListener('click', (e) => {
        this.handleFilter(e.target.dataset.filter);
      });
    });

    // 头部操作按钮
    const syncBtn = document.getElementById('syncBtn');
    const settingsBtn = document.getElementById('settingsBtn');
    const fullscreenBtn = document.getElementById('fullscreenBtn');
    
    syncBtn?.addEventListener('click', () => {
      this.handleSync();
    });
    
    settingsBtn?.addEventListener('click', () => {
      this.handleSettings();
    });
    
    fullscreenBtn?.addEventListener('click', () => {
      this.toggleFullscreen();
    });

    // 底部操作按钮
    const newWindowBtn = document.getElementById('newWindowBtn');
    const exportBtn = document.getElementById('exportBtn');
    const importBtn = document.getElementById('importBtn');
    const duplicatesBtn = document.getElementById('duplicatesBtn');
    
    newWindowBtn?.addEventListener('click', () => {
      this.handleNewWindow();
    });
    
    exportBtn?.addEventListener('click', () => {
      this.handleExport();
    });
    
    importBtn?.addEventListener('click', () => {
      this.handleImport();
    });
    
    duplicatesBtn?.addEventListener('click', () => {
      this.handleFindDuplicates();
    });

    // 键盘快捷键
    document.addEventListener('keydown', (e) => {
      this.handleKeyboard(e);
    });

    // 点击外部关闭上下文菜单
    document.addEventListener('click', (e) => {
      this.hideContextMenu();
    });

    // 窗口大小变化
    window.addEventListener('resize', () => {
      this.handleResize();
    });
  }

  /**
   * 加载浏览器数据
   */
  async loadBrowserData() {
    try {
      this.showLoading(true);
      
      // 从Service Worker获取数据
      const response = await this.sendMessage({
        action: 'GET_BROWSER_DATA'
      });
      
      if (response.success) {
        this.browserData = response.data;
        this.filteredData = this.browserData;
        console.log('Browser data loaded:', this.browserData);
      } else {
        throw new Error(response.error || 'Failed to load browser data');
      }
    } catch (error) {
      console.error('Failed to load browser data:', error);
      this.showError('加载浏览器数据失败');
    } finally {
      this.showLoading(false);
    }
  }

  /**
   * 加载演示数据
   */
  loadDemoData() {
    this.browserData = {
      tabs: {
        1: { id: 1, title: '百度一下，你就知道', url: 'https://www.baidu.com', windowId: 1, active: true, favicon: 'https://www.baidu.com/favicon.ico' },
        2: { id: 2, title: 'GitHub - 代码托管平台', url: 'https://github.com', windowId: 1, active: false, favicon: 'https://github.com/favicon.ico' },
        3: { id: 3, title: 'Stack Overflow - 程序员问答社区', url: 'https://stackoverflow.com', windowId: 2, active: true, favicon: 'https://stackoverflow.com/favicon.ico' },
        4: { id: 4, title: 'MDN Web 文档', url: 'https://developer.mozilla.org', windowId: 2, active: false, favicon: 'https://developer.mozilla.org/favicon.ico' },
        5: { id: 5, title: '知乎 - 有问题，就会有答案', url: 'https://www.zhihu.com', windowId: 1, active: false, favicon: 'https://www.zhihu.com/favicon.ico' },
        6: { id: 6, title: 'B站 - 哔哩哔哩', url: 'https://www.bilibili.com', windowId: 2, active: false, favicon: 'https://www.bilibili.com/favicon.ico' }
      },
      windows: {
        1: { id: 1, name: '主窗口', focused: true },
        2: { id: 2, name: '开发窗口', focused: false }
      },
      tabGroups: {},
      settings: {}
    };
    this.filteredData = this.browserData;
    this.showLoading(false);
  }

  /**
   * 发送消息到Service Worker
   */
  async sendMessage(message) {
    if (typeof chrome === 'undefined' || !chrome.runtime) {
      return { success: false, error: 'Chrome runtime not available' };
    }
    
    return new Promise((resolve) => {
      chrome.runtime.sendMessage(message, (response) => {
        resolve(response || { success: false, error: 'No response' });
      });
    });
  }

  /**
   * 处理搜索
   */
  handleSearch(searchTerm) {
    this.searchTerm = searchTerm.toLowerCase().trim();
    this.updateSearchUI();
    this.filterData();
    this.render();
  }

  /**
   * 清除搜索
   */
  clearSearch() {
    const searchInput = document.getElementById('searchInput');
    if (searchInput) {
      searchInput.value = '';
      this.handleSearch('');
    }
  }

  /**
   * 更新搜索UI
   */
  updateSearchUI() {
    const clearBtn = document.getElementById('clearSearchBtn');
    if (clearBtn) {
      clearBtn.classList.toggle('visible', this.searchTerm.length > 0);
    }
  }

  /**
   * 处理筛选
   */
  handleFilter(filter) {
    this.currentFilter = filter;
    
    // 更新筛选按钮状态
    document.querySelectorAll('.filter-btn').forEach(btn => {
      btn.classList.toggle('active', btn.dataset.filter === filter);
    });
    
    this.filterData();
    this.render();
  }

  /**
   * 筛选数据
   */
  filterData() {
    if (!this.browserData) {
      this.filteredData = null;
      return;
    }

    let filteredWindows = {};
    let filteredTabs = {};
    let filteredGroups = {};

    // 应用搜索筛选
    Object.entries(this.browserData.windows || {}).forEach(([windowId, window]) => {
      const windowMatches = this.matchesSearch(window, 'window');
      const windowTabs = {};
      let hasMatchingTabs = false;

      // 筛选窗口中的标签页
      Object.entries(this.browserData.tabs || {}).forEach(([tabId, tab]) => {
        if (tab.windowId == windowId) {
          const tabMatches = this.matchesSearch(tab, 'tab');
          if (tabMatches) {
            windowTabs[tabId] = tab;
            filteredTabs[tabId] = tab;
            hasMatchingTabs = true;
          }
        }
      });

      // 如果没有搜索条件，或者窗口/标签页匹配，则包含该窗口
      if (!this.searchTerm || windowMatches || hasMatchingTabs) {
        filteredWindows[windowId] = {
          ...window,
          tabs: !this.searchTerm ?
            // 没有搜索条件时，显示该窗口的所有标签页
            Object.fromEntries(
              Object.entries(this.browserData.tabs || {}).filter(([tabId, tab]) => tab.windowId == windowId)
            ) :
            // 有搜索条件时，只显示匹配的标签页
            windowTabs
        };
      }
    });

    // 筛选标签组
    Object.entries(this.browserData.tabGroups || {}).forEach(([groupId, group]) => {
      if (this.matchesSearch(group, 'group')) {
        filteredGroups[groupId] = group;
      }
    });

    // 应用类型筛选
    if (this.currentFilter !== 'all') {
      if (this.currentFilter === 'tabs') {
        filteredWindows = {};
        filteredGroups = {};
      } else if (this.currentFilter === 'windows') {
        filteredTabs = {};
        filteredGroups = {};
      } else if (this.currentFilter === 'groups') {
        filteredWindows = {};
        filteredTabs = {};
      }
    }

    this.filteredData = {
      windows: filteredWindows,
      tabs: filteredTabs,
      tabGroups: filteredGroups,
      settings: this.browserData.settings
    };
  }

  /**
   * 检查项目是否匹配搜索条件
   */
  matchesSearch(item, type) {
    if (!this.searchTerm) return true;

    const searchFields = [];

    if (type === 'tab') {
      searchFields.push(
        item.title || '',
        item.url || '',
        item.notes || '',
        ...(item.tags || [])
      );
    } else if (type === 'window') {
      searchFields.push(
        item.name || '',
        item.description || ''
      );
    } else if (type === 'group') {
      searchFields.push(
        item.title || '',
        item.description || '',
        ...(item.tags || [])
      );
    }

    return searchFields.some(field =>
      field.toLowerCase().includes(this.searchTerm)
    );
  }

  /**
   * 渲染界面
   */
  render() {
    this.renderStats();
    this.renderContent();
  }

  /**
   * 渲染统计信息
   */
  renderStats() {
    const totalTabsEl = document.getElementById('totalTabs');
    const totalWindowsEl = document.getElementById('totalWindows');
    const totalGroupsEl = document.getElementById('totalGroups');
    const totalMemoryEl = document.getElementById('totalMemory');

    if (!this.filteredData) {
      totalTabsEl.textContent = '0';
      totalWindowsEl.textContent = '0';
      totalGroupsEl.textContent = '0';
      totalMemoryEl.textContent = '0MB';
      return;
    }

    const tabCount = Object.keys(this.filteredData.tabs || {}).length;
    const windowCount = Object.keys(this.filteredData.windows || {}).length;
    const groupCount = Object.keys(this.filteredData.tabGroups || {}).length;
    const estimatedMemory = tabCount * 50; // 估算每个标签页50MB

    totalTabsEl.textContent = tabCount;
    totalWindowsEl.textContent = windowCount;
    totalGroupsEl.textContent = groupCount;
    totalMemoryEl.textContent = `${estimatedMemory}MB`;
  }

  /**
   * 渲染主要内容
   */
  renderContent() {
    const windowsList = document.getElementById('windowsList');
    const emptyState = document.getElementById('emptyState');

    if (!this.filteredData || Object.keys(this.filteredData.windows || {}).length === 0) {
      windowsList.style.display = 'none';
      emptyState.style.display = 'flex';
      return;
    }

    emptyState.style.display = 'none';
    windowsList.style.display = 'block';
    windowsList.innerHTML = '';

    // 渲染每个窗口
    Object.entries(this.filteredData.windows).forEach(([windowId, window]) => {
      const windowElement = this.createWindowElement(windowId, window);
      windowsList.appendChild(windowElement);
    });
  }

  /**
   * 创建窗口元素
   */
  createWindowElement(windowId, window) {
    const windowDiv = document.createElement('div');
    windowDiv.className = 'window-item';
    windowDiv.dataset.windowId = windowId;

    const tabCount = Object.keys(window.tabs || {}).length;
    const windowTitle = window.name || `窗口 ${windowId}`;
    const windowMeta = `${tabCount} 个标签页`;

    windowDiv.innerHTML = `
      <div class="window-header" onclick="tabManagerApp.toggleWindow('${windowId}')">
        <div class="window-info">
          <svg class="window-icon" viewBox="0 0 24 24" fill="currentColor">
            <path d="M4 6H2v14c0 1.1.9 2 2 2h14v-2H4V6zm16-4H8c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm-1 9H9V9h10v2zm-4 4H9v-2h6v2zm4-8H9V5h10v2z"/>
          </svg>
          <div>
            <div class="window-title">${this.escapeHtml(windowTitle)}</div>
            <div class="window-meta">${windowMeta}</div>
          </div>
        </div>
        <div class="window-actions">
          <button class="btn btn-icon" onclick="event.stopPropagation(); tabManagerApp.focusWindow('${windowId}')" title="聚焦窗口">
            <svg viewBox="0 0 24 24" fill="currentColor">
              <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
            </svg>
          </button>
          <button class="btn btn-icon" onclick="event.stopPropagation(); tabManagerApp.showWindowMenu('${windowId}', event)" title="更多选项">
            <svg viewBox="0 0 24 24" fill="currentColor">
              <path d="M12 8c1.1 0 2-.9 2-2s-.9-2-2-2-2 .9-2 2 .9 2 2 2zm0 2c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zm0 6c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2z"/>
            </svg>
          </button>
        </div>
      </div>
      <div class="window-tabs" id="windowTabs-${windowId}" style="display: block;">
        <div class="tabs-container">
          ${this.createTabsHTML(window.tabs || {})}
        </div>
      </div>
    `;

    return windowDiv;
  }

  /**
   * 创建标签页HTML - 横向布局
   */
  createTabsHTML(tabs) {
    return Object.entries(tabs).map(([tabId, tab]) => {
      const favicon = tab.favicon || 'data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path fill="%23666" d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2z"/></svg>';
      const isActive = tab.active ? 'active' : '';

      return `
        <div class="tab-item ${isActive}" data-tab-id="${tabId}" onclick="tabManagerApp.activateTab('${tabId}')">
          <img class="tab-favicon" src="${favicon}" alt="" onerror="this.src='data:image/svg+xml,<svg xmlns=\\"http://www.w3.org/2000/svg\\" viewBox=\\"0 0 24 24\\"><path fill=\\"%23666\\" d=\\"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2z\\"/></svg>'">
          <div class="tab-info">
            <div class="tab-title">${this.escapeHtml(tab.title || 'Untitled')}</div>
            <div class="tab-url">${this.escapeHtml(this.formatUrl(tab.url || ''))}</div>
          </div>
          <div class="tab-actions">
            <div class="tab-close" onclick="event.stopPropagation(); tabManagerApp.closeTab('${tabId}')" title="Close Tab">
              <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
              </svg>
            </div>
          </div>
        </div>
      `;
    }).join('');
  }

  /**
   * 显示/隐藏加载状态
   */
  showLoading(show) {
    const loadingState = document.getElementById('loadingState');
    const mainContent = document.querySelector('.main-content');

    if (show) {
      loadingState.style.display = 'flex';
    } else {
      loadingState.style.display = 'none';
    }

    this.isLoading = show;
  }

  /**
   * 显示错误信息
   */
  showError(message) {
    this.showNotification(message, 'error');
  }

  /**
   * 显示通知
   */
  showNotification(message, type = 'info') {
    const container = document.getElementById('notificationContainer');
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.innerHTML = `
      <div class="notification-content">
        <span>${message}</span>
        <button class="notification-close" onclick="this.parentElement.parentElement.remove()">×</button>
      </div>
    `;

    container.appendChild(notification);

    // 自动移除通知
    setTimeout(() => {
      if (notification.parentNode) {
        notification.parentNode.removeChild(notification);
      }
    }, 5000);
  }

  /**
   * 转义HTML
   */
  escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
  }

  /**
   * 格式化URL显示
   */
  formatUrl(url) {
    try {
      const urlObj = new URL(url);
      return urlObj.hostname + urlObj.pathname;
    } catch {
      return url;
    }
  }

  // 事件处理方法

  /**
   * 切换窗口展开/折叠
   */
  toggleWindow(windowId) {
    const tabsContainer = document.getElementById(`windowTabs-${windowId}`);
    if (tabsContainer) {
      const isVisible = tabsContainer.style.display === 'block';
      tabsContainer.style.display = isVisible ? 'none' : 'block';

      // 更新窗口头部的展开/折叠指示器
      const windowHeader = tabsContainer.previousElementSibling;
      if (windowHeader) {
        windowHeader.classList.toggle('expanded', !isVisible);
      }
    }
  }

  /**
   * 激活标签页
   */
  async activateTab(tabId) {
    try {
      if (typeof chrome !== 'undefined' && chrome.runtime) {
        const response = await this.sendMessage({
          action: 'ACTIVATE_TAB',
          data: { tabId: parseInt(tabId) }
        });

        if (response.success) {
          this.showNotification('标签页已激活', 'success');
        } else {
          this.showError('激活标签页失败');
        }
      } else {
        this.showNotification('标签页已激活（演示模式）', 'info');
      }
    } catch (error) {
      console.error('Failed to activate tab:', error);
      this.showError('激活标签页失败');
    }
  }

  /**
   * 关闭标签页
   */
  async closeTab(tabId) {
    try {
      if (typeof chrome !== 'undefined' && chrome.runtime) {
        const response = await this.sendMessage({
          action: 'CLOSE_TABS',
          data: { tabIds: [parseInt(tabId)] }
        });

        if (response.success) {
          this.showNotification('标签页已关闭', 'success');
          // 重新加载数据
          await this.loadBrowserData();
          this.render();
        } else {
          this.showError('关闭标签页失败');
        }
      } else {
        // 演示模式 - 从数据中移除
        delete this.browserData.tabs[tabId];
        this.filterData();
        this.render();
        this.showNotification('标签页已关闭（演示模式）', 'info');
      }
    } catch (error) {
      console.error('Failed to close tab:', error);
      this.showError('关闭标签页失败');
    }
  }

  /**
   * 聚焦窗口
   */
  async focusWindow(windowId) {
    try {
      if (typeof chrome !== 'undefined' && chrome.runtime) {
        const response = await this.sendMessage({
          action: 'FOCUS_WINDOW',
          data: { windowId: parseInt(windowId) }
        });

        if (response.success) {
          this.showNotification('Window focused', 'success');
        } else {
          this.showError('Failed to focus window');
        }
      } else {
        this.showNotification('Window focused (demo mode)', 'info');
      }
    } catch (error) {
      console.error('Failed to focus window:', error);
      this.showError('Failed to focus window');
    }
  }

  /**
   * 显示窗口菜单
   */
  showWindowMenu(windowId, event) {
    event.preventDefault();
    event.stopPropagation();

    const contextMenu = document.getElementById('contextMenu');
    contextMenu.innerHTML = `
      <div class="context-menu-item" onclick="tabManagerApp.newTabInWindow('${windowId}')">
        <svg viewBox="0 0 24 24" fill="currentColor">
          <path d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z"/>
        </svg>
        新建标签页
      </div>
      <div class="context-menu-item" onclick="tabManagerApp.closeWindow('${windowId}')">
        <svg viewBox="0 0 24 24" fill="currentColor">
          <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
        </svg>
        关闭窗口
      </div>
    `;

    contextMenu.style.display = 'block';
    contextMenu.style.left = event.pageX + 'px';
    contextMenu.style.top = event.pageY + 'px';
  }

  /**
   * 隐藏上下文菜单
   */
  hideContextMenu() {
    const contextMenu = document.getElementById('contextMenu');
    contextMenu.style.display = 'none';
  }

  /**
   * 在窗口中创建新标签页
   */
  async newTabInWindow(windowId) {
    try {
      if (typeof chrome !== 'undefined' && chrome.runtime) {
        const response = await this.sendMessage({
          action: 'CREATE_TAB',
          data: {
            windowId: parseInt(windowId),
            url: 'chrome://newtab/'
          }
        });

        if (response.success) {
          this.showNotification('新标签页已创建', 'success');
          await this.loadBrowserData();
          this.render();
        } else {
          this.showError('创建新标签页失败');
        }
      } else {
        this.showNotification('新标签页已创建（演示模式）', 'info');
      }
    } catch (error) {
      console.error('Failed to create new tab:', error);
      this.showError('创建新标签页失败');
    }

    this.hideContextMenu();
  }

  /**
   * 关闭窗口
   */
  async closeWindow(windowId) {
    try {
      if (typeof chrome !== 'undefined' && chrome.runtime) {
        const response = await this.sendMessage({
          action: 'CLOSE_WINDOW',
          data: { windowId: parseInt(windowId) }
        });

        if (response.success) {
          this.showNotification('Window closed', 'success');
          await this.loadBrowserData();
          this.render();
        } else {
          this.showError('Failed to close window');
        }
      } else {
        // 演示模式 - 从数据中移除
        delete this.browserData.windows[windowId];
        this.filterData();
        this.render();
        this.showNotification('Window closed (demo mode)', 'info');
      }
    } catch (error) {
      console.error('Failed to close window:', error);
      this.showError('Failed to close window');
    }

    this.hideContextMenu();
  }

  /**
   * 处理同步
   */
  async handleSync() {
    try {
      this.showLoading(true);

      if (typeof chrome !== 'undefined' && chrome.runtime) {
        const response = await this.sendMessage({ action: 'SYNC_DATA' });

        if (response.success) {
          await this.loadBrowserData();
          this.render();
          this.showNotification('数据同步成功', 'success');
        } else {
          this.showError('数据同步失败');
        }
      } else {
        // 演示模式 - 模拟同步
        setTimeout(() => {
          this.showNotification('数据已同步（演示模式）', 'info');
          this.showLoading(false);
        }, 1000);
        return;
      }
    } catch (error) {
      console.error('Sync failed:', error);
      this.showError('数据同步失败');
    } finally {
      this.showLoading(false);
    }
  }

  /**
   * 处理设置
   */
  handleSettings() {
    this.showNotification('设置功能即将推出', 'info');
  }

  /**
   * 切换全屏模式
   */
  toggleFullscreen() {
    if (!document.fullscreenElement) {
      document.documentElement.requestFullscreen();
      this.isFullscreen = true;
    } else {
      document.exitFullscreen();
      this.isFullscreen = false;
    }
  }

  /**
   * 处理新窗口
   */
  async handleNewWindow() {
    try {
      if (typeof chrome !== 'undefined' && chrome.runtime) {
        const response = await this.sendMessage({
          action: 'CREATE_WINDOW',
          data: { url: 'chrome://newtab/' }
        });

        if (response.success) {
          this.showNotification('新窗口已创建', 'success');
          await this.loadBrowserData();
          this.render();
        } else {
          this.showError('创建新窗口失败');
        }
      } else {
        this.showNotification('新窗口已创建（演示模式）', 'info');
      }
    } catch (error) {
      console.error('Failed to create new window:', error);
      this.showError('创建新窗口失败');
    }
  }

  /**
   * 处理导出
   */
  handleExport() {
    this.showNotification('导出功能即将推出', 'info');
  }

  /**
   * 处理导入
   */
  handleImport() {
    this.showNotification('导入功能即将推出', 'info');
  }

  /**
   * 处理查找重复标签页
   */
  handleFindDuplicates() {
    this.showNotification('查找重复功能即将推出', 'info');
  }

  /**
   * 处理键盘快捷键
   */
  handleKeyboard(event) {
    // Ctrl/Cmd + F: 聚焦搜索框
    if ((event.ctrlKey || event.metaKey) && event.key === 'f') {
      event.preventDefault();
      const searchInput = document.getElementById('searchInput');
      searchInput?.focus();
    }

    // Escape: 清除搜索或关闭菜单
    if (event.key === 'Escape') {
      const contextMenu = document.getElementById('contextMenu');
      if (contextMenu.style.display === 'block') {
        this.hideContextMenu();
      } else {
        this.clearSearch();
      }
    }

    // F11: 切换全屏
    if (event.key === 'F11') {
      event.preventDefault();
      this.toggleFullscreen();
    }
  }

  /**
   * 处理窗口大小变化
   */
  handleResize() {
    // 响应式布局调整
    const isMobile = window.innerWidth < 768;
    document.body.classList.toggle('mobile', isMobile);
  }
}

// 初始化应用
let tabManagerApp;
document.addEventListener('DOMContentLoaded', () => {
  tabManagerApp = new TabManagerApp();
});
